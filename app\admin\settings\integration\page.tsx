"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  AlertCircle,
  Check,
  Download,
  Search,
  ChevronDown,
  Info,
  AlertTriangle,
  XCircle,
  Eye,
  Settings,
  Plus,
  Phone,
  Video,
  MessageSquare,
  BrainCircuit,
  Webhook,
  Store,
  SquareTerminal,
  ShoppingCart,
  Package,
  BarChart3,
  CreditCardIcon,
  Wallet,
  Receipt,
  Users,
  FileText,
  Building,
  Globe,
  BarChart,
  Truck,
  Mail,
  MessageCircle,
  Calendar,
  Headphones,
  Save,
  Loader2,
} from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Checkbox } from "@/components/ui/checkbox"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { toast } from "@/hooks/use-toast"

// Типы интеграций
type IntegrationType =
  | "payment"
  | "communication"
  | "crm"
  | "analytics"
  | "logistics"
  | "ecommerce"
  | "marketing"
  | "support"

// Интерфейс для интеграции
interface Integration {
  id: string
  name: string
  description: string
  type: IntegrationType
  isActive: boolean
  isConfigured: boolean
  icon: React.ElementType
  apiKey?: string
  apiSecret?: string
  webhookUrl?: string
  lastSynced?: string
  category: string
}

// Типы событий
type EventType = "info" | "warning" | "error"

// Интерфейс для события
interface IntegrationEvent {
  id: string
  type: EventType
  source: string
  message: string
  timestamp: string
  details?: string
  resolved: boolean
}

// Интерфейс для настроек SignalWire
interface SignalWireSettings {
  projectId: string
  apiToken: string
  spaceUrl: string
  phoneNumbers: PhoneNumber[]
  voiceSettings: VoiceSettings
  videoSettings: VideoSettings
  messagingSettings: MessagingSettings
  aiSettings: AISettings
  webhooks: SignalWireWebhook[]
}

// Интерфейс для настроек Stripe
interface StripeSettings {
  secretKey: string
  publishableKey: string
  testMode: boolean
  defaultCurrency: string
  paymentMethods: string[]
  automaticCapture: boolean
  threeDSecure: boolean
  subscriptionPlans: SubscriptionPlan[]
  invoiceSettings: InvoiceSettings
  webhooks: StripeWebhook[]
}

// Интерфейс для настроек Clover
interface CloverSettings {
  merchantId: string
  apiToken: string
  environment: "production" | "sandbox"
  terminalSettings: TerminalSettings
  onlinePaymentSettings: OnlinePaymentSettings
  inventorySync: InventorySync
  reportingSettings: ReportingSettings
}

// Интерфейс для настроек Square
interface SquareSettings {
  applicationId: string
  accessToken: string
  locationId: string
  paymentMethods: string[]
  autoTip: boolean
  splitPayments: boolean
  catalogSync: CatalogSync
  customerManagement: CustomerManagement
  invoiceSettings: SquareInvoiceSettings
}

// Интерфейс для настроек терминала Clover
interface TerminalSettings {
  terminals: Terminal[]
  receiptSettings: ReceiptSettings
  tipping: boolean
}

// Интерфейс для настроек онлайн платежей Clover
interface OnlinePaymentSettings {
  supportedCards: string[]
  securitySettings: SecuritySettings
  tokenization: boolean
}

// Интерфейс для синхронизации инвентаря Clover
interface InventorySync {
  syncProducts: boolean
  categories: string[]
  modifiers: string[]
}

// Интерфейс для настроек отчетности Clover
interface ReportingSettings {
  reportFrequency: string
  exportData: boolean
}

// Интерфейс для настроек каталога Square
interface CatalogSync {
  syncProducts: boolean
  variations: boolean
  taxes: boolean
}

// Интерфейс для управления клиентами Square
interface CustomerManagement {
  manageCustomers: boolean
  loyaltyPrograms: boolean
}

// Интерфейс для настроек счетов Square
interface SquareInvoiceSettings {
  invoiceDesign: string
  reminders: boolean
}

// Интерфейс для терминала Clover
interface Terminal {
  id: string
  name: string
}

// Интерфейс для настроек чеков Clover
interface ReceiptSettings {
  header: string
  footer: string
}

// Интерфейс для настроек безопасности Clover
interface SecuritySettings {
  cvvRequired: boolean
  addressVerification: boolean
}

// Интерфейс для вебхука Stripe
interface StripeWebhook {
  id: string
  name: string
  endpoint: string
  events: string[]
}

// Интерфейс для плана подписки Stripe
interface SubscriptionPlan {
  id: string
  name: string
  price: number
  interval: string
  trialPeriod: number
}

// Интерфейс для настроек счетов Stripe
interface InvoiceSettings {
  issueDate: string
  dueDate: string
  taxRate: number
  numbering: string
}

// Интерфейс для телефонного номера
interface PhoneNumber {
  id: string
  number: string
  type: "local" | "tollfree" | "international"
  capabilities: ("voice" | "sms" | "fax")[]
  isDefault: boolean
}

// Интерфейс для настроек голоса
interface VoiceSettings {
  defaultCallerName: string
  defaultCallerNumber: string
  recordCalls: boolean
  transcribeCalls: boolean
  voiceLanguage: string
  voiceGender: "male" | "female"
  greetingMessage: string
  voicemailEnabled: boolean
  voicemailMessage: string
  callRoutingType: "sequential" | "simultaneous" | "round-robin"
}

// Интерфейс для настроек видео
interface VideoSettings {
  defaultRoomType: "peer-to-peer" | "small-group" | "large-group"
  maxParticipants: number
  recordSessions: boolean
  enableScreenSharing: boolean
  enableChat: boolean
  autoRecording: boolean
  customLayout: boolean
  brandingEnabled: boolean
  brandingLogo?: string
  brandingColor?: string
}

// Интерфейс для настроек сообщений
interface MessagingSettings {
  defaultSenderName: string
  defaultSenderNumber: string
  autoResponder: boolean
  autoResponderMessage: string
  deliveryReports: boolean
  inboundMessageWebhook: string
  outboundMessageWebhook: string
}

// Интерфейс для настроек AI
interface AISettings {
  enableVoiceAI: boolean
  enableTextAI: boolean
  defaultAIModel: string
  customPrompts: boolean
  contextRetention: boolean
  contextRetentionDuration: number
  sentimentAnalysis: boolean
  languageDetection: boolean
  contentModeration: boolean
}

// Интерфейс для вебхука
interface SignalWireWebhook {
  id: string
  name: string
  url: string
  events: string[]
  isActive: boolean
  secret?: string
}

// Интерфейс для API учетных данных
interface ApiCredentials {
  apiKey: string
  apiSecret?: string
  apiToken?: string
  clientId?: string
  clientSecret?: string
  accessToken?: string
  refreshToken?: string
  webhookSecret?: string
  environment: "production" | "sandbox" | "development"
}

export default function IntegrationPage() {
  // Состояние для SignalWire
  const [signalWireSettings, setSignalWireSettings] = useState<SignalWireSettings>({
    projectId: "",
    apiToken: "",
    spaceUrl: "",
    phoneNumbers: [],
    voiceSettings: {
      defaultCallerName: "Ваша компания",
      defaultCallerNumber: "",
      recordCalls: false,
      transcribeCalls: false,
      voiceLanguage: "ru-RU",
      voiceGender: "female",
      greetingMessage: "Здравствуйте! Благодарим за звонок в нашу компанию.",
      voicemailEnabled: true,
      voicemailMessage:
        "К сожалению, мы не можем ответить на ваш звонок прямо сейчас. Пожалуйста, оставьте сообщение после сигнала.",
      callRoutingType: "sequential",
    },
    videoSettings: {
      defaultRoomType: "small-group",
      maxParticipants: 10,
      recordSessions: false,
      enableScreenSharing: true,
      enableChat: true,
      autoRecording: false,
      customLayout: false,
      brandingEnabled: false,
    },
    messagingSettings: {
      defaultSenderName: "Ваша компания",
      defaultSenderNumber: "",
      autoResponder: false,
      autoResponderMessage: "Спасибо за ваше сообщение. Мы свяжемся с вами в ближайшее время.",
      deliveryReports: true,
      inboundMessageWebhook: "",
      outboundMessageWebhook: "",
    },
    aiSettings: {
      enableVoiceAI: false,
      enableTextAI: false,
      defaultAIModel: "gpt-3.5-turbo",
      customPrompts: false,
      contextRetention: true,
      contextRetentionDuration: 30,
      sentimentAnalysis: false,
      languageDetection: true,
      contentModeration: false,
    },
    webhooks: [],
  })

  // Состояние для Stripe
  const [stripeSettings, setStripeSettings] = useState<StripeSettings>({
    secretKey: "",
    publishableKey: "",
    testMode: true,
    defaultCurrency: "USD",
    paymentMethods: ["card", "alipay", "wechat_pay"],
    automaticCapture: true,
    threeDSecure: true,
    subscriptionPlans: [],
    invoiceSettings: {
      issueDate: new Date().toISOString(),
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      taxRate: 0.0725,
      numbering: "sequential",
    },
    webhooks: [],
  })

  // Состояние для Clover
  const [cloverSettings, setCloverSettings] = useState<CloverSettings>({
    merchantId: "",
    apiToken: "",
    environment: "sandbox",
    terminalSettings: {
      terminals: [],
      receiptSettings: {
        header: "Спасибо за покупку!",
        footer: "Мы ждем вас снова!",
      },
      tipping: true,
    },
    onlinePaymentSettings: {
      supportedCards: ["visa", "mastercard", "amex"],
      securitySettings: {
        cvvRequired: true,
        addressVerification: true,
      },
      tokenization: true,
    },
    inventorySync: {
      syncProducts: true,
      categories: [],
      modifiers: [],
    },
    reportingSettings: {
      reportFrequency: "daily",
      exportData: true,
    },
  })

  // Состояние для Square
  const [squareSettings, setSquareSettings] = useState<SquareSettings>({
    applicationId: "",
    accessToken: "",
    locationId: "",
    paymentMethods: ["card", "cash", "gift_card"],
    autoTip: true,
    splitPayments: false,
    catalogSync: {
      syncProducts: true,
      variations: true,
      taxes: true,
    },
    customerManagement: {
      manageCustomers: true,
      loyaltyPrograms: true,
    },
    invoiceSettings: {
      invoiceDesign: "modern",
      reminders: true,
    },
  })

  // Состояние для отслеживания активной вкладки настроек SignalWire
  const [activeSignalWireTab, setActiveSignalWireTab] = useState<string>("general")

  // Состояние для отслеживания активной вкладки настроек Stripe
  const [activeStripeTab, setActiveStripeTab] = useState<string>("general")

  // Состояние для отслеживания активной вкладки настроек Clover
  const [activeCloverTab, setActiveCloverTab] = useState<string>("general")

  // Состояние для отслеживания активной вкладки настроек Square
  const [activeSquareTab, setActiveSquareTab] = useState<string>("general")

  // Состояние для отслеживания редактируемого телефонного номера
  const [editingPhoneNumber, setEditingPhoneNumber] = useState<PhoneNumber | null>(null)

  // Состояние для отслеживания редактируемого вебхука
  const [editingWebhook, setEditingWebhook] = useState<SignalWireWebhook | null>(null)

  // Состояние для отображения диалога добавления телефонного номера
  const [showAddPhoneNumberDialog, setShowAddPhoneNumberDialog] = useState<boolean>(false)

  // Состояние для отображения диалога добавления вебхука
  const [showAddWebhookDialog, setShowAddWebhookDialog] = useState<boolean>(false)

  // Состояние для отслеживания статуса тестирования соединения
  const [connectionTestStatus, setConnectionTestStatus] = useState<"idle" | "testing" | "success" | "error">("idle")

  // Состояние для отслеживания процесса сохранения
  const [isSaving, setIsSaving] = useState<boolean>(false)

  // Состояние для отслеживания API учетных данных
  const [apiCredentials, setApiCredentials] = useState<Record<string, ApiCredentials>>({
    signalwire: {
      apiKey: "",
      apiSecret: "",
      apiToken: "",
      environment: "production",
    },
    stripe: {
      apiKey: "",
      apiSecret: "",
      webhookSecret: "",
      environment: "production",
    },
    clover: {
      apiKey: "",
      apiSecret: "",
      accessToken: "",
      environment: "production",
    },
    square: {
      apiKey: "",
      apiSecret: "",
      accessToken: "",
      environment: "production",
    },
  })

  // Список событий интеграций
  const [events, setEvents] = useState<IntegrationEvent[]>([
    {
      id: "evt-011",
      type: "info",
      source: "Система",
      message: "Запуск системы",
      timestamp: "2023-05-24T15:00:00",
      details: "Система успешно запущена и готова к работе.",
      resolved: true,
    },
    {
      id: "evt-012",
      type: "warning",
      source: "Безопасность",
      message: "Попытка несанкционированного доступа",
      timestamp: "2023-05-24T14:45:00",
      details: "Зафиксирована попытка входа с неизвестного IP-адреса. Доступ заблокирован.",
      resolved: true,
    },
    {
      id: "evt-013",
      type: "error",
      source: "База данных",
      message: "Ошибка подключения к БД",
      timestamp: "2023-05-24T13:30:00",
      details: "Не удалось установить соединение с базой данных. Проверьте настройки подключения.",
      resolved: false,
    },
    {
      id: "evt-014",
      type: "info",
      source: "Пользователи",
      message: "Новая регистрация",
      timestamp: "2023-05-24T12:15:00",
      details: "Зарегистрирован новый пользователь в системе.",
      resolved: true,
    },
    {
      id: "evt-015",
      type: "error",
      source: "Файловое хранилище",
      message: "Ошибка загрузки файла",
      timestamp: "2023-05-24T11:00:00",
      details: "Не удалось загрузить файл. Недостаточно места в хранилище.",
      resolved: false,
    },
    {
      id: "evt-016",
      type: "warning",
      source: "Сеть",
      message: "Высокая нагрузка на сеть",
      timestamp: "2023-05-24T10:30:00",
      details: "Обнаружена высокая нагрузка на сетевую инфраструктуру. Возможно замедление работы системы.",
      resolved: true,
    },
    {
      id: "evt-017",
      type: "info",
      source: "Обновления",
      message: "Доступно новое обновление",
      timestamp: "2023-05-24T09:45:00",
      details: "Доступно обновление системы до версии 2.5.0. Рекомендуется установить.",
      resolved: true,
    },
    {
      id: "evt-018",
      type: "error",
      source: "API",
      message: "Ошибка в API",
      timestamp: "2023-05-24T08:30:00",
      details: "Обнаружена ошибка в API. Эндпоинт /api/users возвращает код 500.",
      resolved: false,
    },
    {
      id: "evt-019",
      type: "warning",
      source: "Производительность",
      message: "Низкая производительность",
      timestamp: "2023-05-24T07:15:00",
      details: "Обнаружена низкая производительность системы. Среднее время ответа увеличилось до 2 секунд.",
      resolved: false,
    },
    {
      id: "evt-020",
      type: "info",
      source: "Резервное копирование",
      message: "Создана резервная копия",
      timestamp: "2023-05-24T06:00:00",
      details: "Успешно создана резервная копия всех данных системы. Размер: 2.5 ГБ.",
      resolved: true,
    },
    {
      id: "evt-021",
      type: "info",
      source: "SignalWire",
      message: "Интеграция настроена",
      timestamp: "2023-05-25T10:00:00",
      details: "Интеграция с SignalWire успешно настроена и активирована.",
      resolved: true,
    },
    {
      id: "evt-022",
      type: "info",
      source: "SignalWire",
      message: "Входящий звонок",
      timestamp: "2023-05-25T11:30:00",
      details: "Получен входящий звонок от +7 (999) 123-45-67. Звонок успешно обработан.",
      resolved: true,
    },
    {
      id: "evt-023",
      type: "warning",
      source: "SignalWire",
      message: "Проблема с качеством звука",
      timestamp: "2023-05-25T12:15:00",
      details: "Обнаружена проблема с качеством звука во время звонка. Возможно, проблемы с сетевым соединением.",
      resolved: false,
    },
  ])

  // Состояние для отслеживания выбранного события
  const [selectedEvent, setSelectedEvent] = useState<IntegrationEvent | null>(null)

  // Состояние для отслеживания активной вкладки
  const [eventFilters, setEventFilters] = useState({
    type: "all",
    source: "all",
    resolved: "all",
    search: "",
  })

  // Фильтрация событий
  const filteredEvents = events.filter((event) => {
    // Фильтр по типу
    if (eventFilters.type !== "all" && event.type !== eventFilters.type) {
      return false
    }

    // Фильтр по источнику
    if (eventFilters.source !== "all" && event.source !== eventFilters.source) {
      return false
    }

    // Фильтр по статусу разрешения
    if (eventFilters.resolved !== "all") {
      const isResolved = eventFilters.resolved === "resolved"
      if (event.resolved !== isResolved) {
        return false
      }
    }

    // Фильтр по поисковому запросу
    if (eventFilters.search) {
      const searchLower = eventFilters.search.toLowerCase()
      return (
        event.message.toLowerCase().includes(searchLower) ||
        event.source.toLowerCase().includes(searchLower) ||
        (event.details && event.details.toLowerCase().includes(searchLower))
      )
    }

    return true
  })

  // Обработчик просмотра деталей события
  const handleViewEventDetails = (event: IntegrationEvent) => {
    setSelectedEvent(event)
  }

  // Обработчик закрытия диалога с деталями события
  const handleCloseEventDetails = () => {
    setSelectedEvent(null)
  }

  // Обработчик изменения статуса разрешения события
  const handleToggleEventResolved = (id: string) => {
    setEvents(events.map((event) => (event.id === id ? { ...event, resolved: !event.resolved } : event)))
  }

  // Обработчик экспорта журнала событий
  const handleExportEvents = () => {
    // В реальном приложении здесь был бы код для экспорта данных в CSV или JSON
    toast({
      title: "Экспорт журнала событий",
      description: "Журнал событий успешно экспортирован",
    })
  }

  // Обработчик изменения фильтра типа события
  const handleEventTypeFilterChange = (value: string) => {
    setEventFilters({
      ...eventFilters,
      type: value as "all" | "info" | "warning" | "error",
    })
  }

  // Обработчик изменения фильтра источника события
  const handleEventSourceFilterChange = (value: string) => {
    setEventFilters({
      ...eventFilters,
      source: value,
    })
  }

  // Обработчик изменения фильтра статуса разрешения события
  const handleEventResolvedFilterChange = (value: string) => {
    setEventFilters({
      ...eventFilters,
      resolved: value as "all" | "resolved" | "unresolved",
    })
  }

  // Обработчик изменения поискового запроса
  const handleSearchChange = (value: string) => {
    setEventFilters({
      ...eventFilters,
      search: value,
    })
  }

  // Обработчик изменения настроек SignalWire
  const handleSignalWireSettingsChange = (field: keyof SignalWireSettings, value: any) => {
    setSignalWireSettings({
      ...signalWireSettings,
      [field]: value,
    })
  }

  // Обработчик изменения настроек голоса
  const handleVoiceSettingsChange = (field: keyof VoiceSettings, value: any) => {
    setSignalWireSettings({
      ...signalWireSettings,
      voiceSettings: {
        ...signalWireSettings.voiceSettings,
        [field]: value,
      },
    })
  }

  // Обработчик изменения настроек видео
  const handleVideoSettingsChange = (field: keyof VideoSettings, value: any) => {
    setSignalWireSettings({
      ...signalWireSettings,
      videoSettings: {
        ...signalWireSettings.videoSettings,
        [field]: value,
      },
    })
  }

  // Обработчик изменения настроек сообщений
  const handleMessagingSettingsChange = (field: keyof MessagingSettings, value: any) => {
    setSignalWireSettings({
      ...signalWireSettings,
      messagingSettings: {
        ...signalWireSettings.messagingSettings,
        [field]: value,
      },
    })
  }

  // Обработчик изменения настроек AI
  const handleAISettingsChange = (field: keyof AISettings, value: any) => {
    setSignalWireSettings({
      ...signalWireSettings,
      aiSettings: {
        ...signalWireSettings.aiSettings,
        [field]: value,
      },
    })
  }

  // Обработчик добавления телефонного номера
  const handleAddPhoneNumber = (phoneNumber: PhoneNumber) => {
    setSignalWireSettings({
      ...signalWireSettings,
      phoneNumbers: [...signalWireSettings.phoneNumbers, phoneNumber],
    })
    setShowAddPhoneNumberDialog(false)
  }

  // Обработчик удаления телефонного номера
  const handleDeletePhoneNumber = (id: string) => {
    setSignalWireSettings({
      ...signalWireSettings,
      phoneNumbers: signalWireSettings.phoneNumbers.filter((number) => number.id !== id),
    })
  }

  // Обработчик редактирования телефонного номера
  const handleEditPhoneNumber = (phoneNumber: PhoneNumber) => {
    setSignalWireSettings({
      ...signalWireSettings,
      phoneNumbers: signalWireSettings.phoneNumbers.map((number) =>
        number.id === phoneNumber.id ? phoneNumber : number,
      ),
    })
    setEditingPhoneNumber(null)
  }

  // Обработчик добавления вебхука
  const handleAddWebhook = (webhook: SignalWireWebhook) => {
    setSignalWireSettings({
      ...signalWireSettings,
      webhooks: [...signalWireSettings.webhooks, webhook],
    })
    setShowAddWebhookDialog(false)
  }

  // Обработчик удаления вебхука
  const handleDeleteWebhook = (id: string) => {
    setSignalWireSettings({
      ...signalWireSettings,
      webhooks: signalWireSettings.webhooks.filter((webhook) => webhook.id !== id),
    })
  }

  // Обработчик редактирования вебхука
  const handleEditWebhook = (webhook: SignalWireWebhook) => {
    setSignalWireSettings({
      ...signalWireSettings,
      webhooks: signalWireSettings.webhooks.map((w) => (w.id === webhook.id ? webhook : w)),
    })
    setEditingWebhook(null)
  }

  // Обработчик изменения активности вебхука
  const handleToggleWebhookActive = (id: string) => {
    setSignalWireSettings({
      ...signalWireSettings,
      webhooks: signalWireSettings.webhooks.map((webhook) =>
        webhook.id === id ? { ...webhook, isActive: !webhook.isActive } : webhook,
      ),
    })
  }

  // Обработчик изменения API учетных данных
  const handleApiCredentialsChange = (integration: string, field: keyof ApiCredentials, value: any) => {
    setApiCredentials({
      ...apiCredentials,
      [integration]: {
        ...apiCredentials[integration],
        [field]: value,
      },
    })
  }

  // Обработчик тестирования соединения с SignalWire
  const handleSaveSignalWireSettings = async () => {
    setIsSaving(true)
    try {
      // Имитация запроса к API
      await new Promise((resolve) => setTimeout(resolve, 1500))

      // Добавляем событие о сохранении настроек
      const newEvent: IntegrationEvent = {
        id: `evt-${Date.now()}`,
        type: "info",
        source: "SignalWire",
        message: "Настройки сохранены",
        timestamp: new Date().toISOString(),
        details: "Настройки интеграции с SignalWire успешно сохранены.",
        resolved: true,
      }
      setEvents([newEvent, ...events])

      // Обновляем статус интеграции
      setIntegrationStatus((prev) => ({
        ...prev,
        signalwire: true,
      }))

      toast({
        title: "Настройки сохранены",
        description: "Настройки SignalWire успешно сохранены",
      })
    } catch (error) {
      toast({
        title: "Ошибка",
        description: "Не удалось сохранить настройки SignalWire",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  // Добавим функции для сохранения настроек для каждой интеграции
  // Найдем функцию handleSaveSignalWireSettings и добавим аналогичные функции для другие интеграций

  // Обработчик сохранения настроек Stripe
  const handleSaveStripeSettings = async () => {
    setIsSaving(true)
    try {
      // Имитация запроса к API
      await new Promise((resolve) => setTimeout(resolve, 1500))

      // Добавляем событие о сохранении настроек
      const newEvent: IntegrationEvent = {
        id: `evt-${Date.now()}`,
        type: "info",
        source: "Stripe",
        message: "Настройки сохранены",
        timestamp: new Date().toISOString(),
        details: "Настройки интеграции с Stripe успешно сохранены.",
        resolved: true,
      }
      setEvents([newEvent, ...events])

      // Обновляем статус интеграции
      setIntegrationStatus((prev) => ({
        ...prev,
        stripe: true,
      }))

      toast({
        title: "Настройки сохранены",
        description: "Настройки Stripe успешно сохранены",
      })
    } catch (error) {
      toast({
        title: "Ошибка",
        description: "Не удалось сохранить настройки Stripe",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  // Обработчик сохранения настроек Clover
  const handleSaveCloverSettings = async () => {
    setIsSaving(true)
    try {
      // Имитация запроса к API
      await new Promise((resolve) => setTimeout(resolve, 1500))

      // Добавляем событие о сохранении настроек
      const newEvent: IntegrationEvent = {
        id: `evt-${Date.now()}`,
        type: "info",
        source: "Clover",
        message: "Настройки сохранены",
        timestamp: new Date().toISOString(),
        details: "Настройки интеграции с Clover успешно сохранены.",
        resolved: true,
      }
      setEvents([newEvent, ...events])

      // Обновляем статус интеграции
      setIntegrationStatus((prev) => ({
        ...prev,
        clover: true,
      }))

      toast({
        title: "Настройки сохранены",
        description: "Настройки Clover успешно сохранены",
      })
    } catch (error) {
      toast({
        title: "Ошибка",
        description: "Не удалось сохранить настройки Clover",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  // Обработчик сохранения настроек Square
  const handleSaveSquareSettings = async () => {
    setIsSaving(true)
    try {
      // Имитация запроса к API
      await new Promise((resolve) => setTimeout(resolve, 1500))

      // Добавляем событие о сохранении настроек
      const newEvent: IntegrationEvent = {
        id: `evt-${Date.now()}`,
        type: "info",
        source: "Square",
        message: "Настройки сохранены",
        timestamp: new Date().toISOString(),
        details: "Настройки интеграции с Square успешно сохранены.",
        resolved: true,
      }
      setEvents([newEvent, ...events])

      // Обновляем статус интеграции
      setIntegrationStatus((prev) => ({
        ...prev,
        square: true,
      }))

      toast({
        title: "Настройки сохранены",
        description: "Настройки Square успешно сохранены",
      })
    } catch (error) {
      toast({
        title: "Ошибка",
        description: "Не удалось сохранить настройки Square",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  // Общая функция для сохранения настроек активной интеграции
  const handleSaveIntegrationSettings = async () => {
    switch (activeIntegration) {
      case "signalwire":
        await handleSaveSignalWireSettings()
        break
      case "stripe":
        await handleSaveStripeSettings()
        break
      case "clover":
        await handleSaveCloverSettings()
        break
      case "square":
        await handleSaveSquareSettings()
        break
      default:
        toast({
          title: "Ошибка",
          description: "Выберите интеграцию для сохранения настроек",
          variant: "destructive",
        })
    }
  }

  // Функция для получения иконки типа события
  const getEventTypeIcon = (type: EventType) => {
    switch (type) {
      case "info":
        return <Info className="h-4 w-4 text-blue-500" />
      case "warning":
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      case "error":
        return <XCircle className="h-4 w-4 text-red-500" />
      default:
        return null
    }
  }

  // Функция для получения цвета бейджа в зависимости от типа события
  const getEventTypeBadgeColor = (type: EventType) => {
    switch (type) {
      case "info":
        return "bg-blue-100 text-blue-800"
      case "warning":
        return "bg-yellow-100 text-yellow-800"
      case "error":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  // Функция для получения названия типа события
  const getEventTypeName = (type: EventType) => {
    switch (type) {
      case "info":
        return "Информация"
      case "warning":
        return "Предупреждение"
      case "error":
        return "Ошибка"
      default:
        return type
    }
  }

  // Функция для форматирования даты и времени
  const formatDateTime = (dateTimeString: string) => {
    const date = new Date(dateTimeString)
    return date.toLocaleString("ru-RU", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    })
  }

  // Получение уникальных источников событий для фильтра
  const uniqueSources = ["all", ...new Set(events.map((event) => event.source))]

  // Генерация уникального ID
  const generateId = () => `id-${Date.now()}-${Math.floor(Math.random() * 1000)}`

  // Состояние для отслеживания активной интеграции
  const [activeIntegration, setActiveIntegration] = useState<string | null>(null)

  // Состояние для отслеживания активной вкладки
  const [activeTab, setActiveTab] = useState<string>("integrations")

  // Состояние для отслеживания активной категории интеграций
  const [activeCategory, setActiveCategory] = useState<string>("all")

  // Состояние для отслеживания статуса подключения интеграций
  const [integrationStatus, setIntegrationStatus] = useState({
    signalwire: false,
    stripe: false,
    clover: false,
    square: false,
  })

  // Состояние для отображения диалога подтверждения сохранения
  const [showSaveConfirmDialog, setShowSaveConfirmDialog] = useState<boolean>(false)

  // Функция для тестирования соединения с интеграцией
  const handleTestConnection = async (integration: string) => {
    // Имитация запроса к API
    await new Promise((resolve) => setTimeout(resolve, 1500))

    // Обновляем статус интеграции
    setIntegrationStatus((prev) => ({
      ...prev,
      [integration]: true,
    }))

    // Добавляем событие об успешном тестировании
    const newEvent = {
      id: `evt-${Date.now()}`,
      type: "info" as "info" | "warning" | "error",
      source: integration.charAt(0).toUpperCase() + integration.slice(1),
      message: "Тестирование соединения",
      timestamp: new Date().toISOString(),
      details: `Тестирование соединения с ${integration} прошло успешно.`,
      resolved: true,
    }

    setEvents([newEvent, ...events])

    toast({
      title: "Соединение установлено",
      description: `Соединение с ${getIntegrationName(integration)} успешно установлено`,
    })
  }

  // Функция для получения иконки интеграции
  const getIntegrationIcon = (integration: string) => {
    switch (integration) {
      case "signalwire":
        return <Phone className="h-6 w-6 text-blue-500" />
      case "stripe":
        return <CreditCardIcon className="h-6 w-6 text-purple-500" />
      case "clover":
        return <Store className="h-6 w-6 text-green-500" />
      case "square":
        return <SquareTerminal className="h-6 w-6 text-blue-500" />
      case "mailchimp":
        return <Mail className="h-6 w-6 text-yellow-500" />
      case "zendesk":
        return <Headphones className="h-6 w-6 text-green-500" />
      case "hubspot":
        return <Building className="h-6 w-6 text-orange-500" />
      case "salesforce":
        return <Users className="h-6 w-6 text-blue-500" />
      case "shopify":
        return <ShoppingCart className="h-6 w-6 text-green-500" />
      case "google-analytics":
        return <BarChart className="h-6 w-6 text-blue-500" />
      case "intercom":
        return <MessageCircle className="h-6 w-6 text-blue-500" />
      case "calendly":
        return <Calendar className="h-6 w-6 text-blue-500" />
      default:
        return <Settings className="h-6 w-6" />
    }
  }

  // Функция для получения цвета интеграции
  const getIntegrationColor = (integration: string) => {
    switch (integration) {
      case "signalwire":
        return "bg-blue-100 text-blue-500 hover:bg-blue-200"
      case "stripe":
        return "bg-purple-100 text-purple-500 hover:bg-purple-200"
      case "clover":
        return "bg-green-100 text-green-500 hover:bg-green-200"
      case "square":
        return "bg-blue-100 text-blue-500 hover:bg-blue-200"
      case "mailchimp":
        return "bg-yellow-100 text-yellow-500 hover:bg-yellow-200"
      case "zendesk":
        return "bg-green-100 text-green-500 hover:bg-green-200"
      case "hubspot":
        return "bg-orange-100 text-orange-500 hover:bg-orange-200"
      case "salesforce":
        return "bg-blue-100 text-blue-500 hover:bg-blue-200"
      case "shopify":
        return "bg-green-100 text-green-500 hover:bg-green-200"
      case "google-analytics":
        return "bg-blue-100 text-blue-500 hover:bg-blue-200"
      case "intercom":
        return "bg-blue-100 text-blue-500 hover:bg-blue-200"
      case "calendly":
        return "bg-blue-100 text-blue-500 hover:bg-blue-200"
      default:
        return "bg-gray-100 text-gray-500 hover:bg-gray-200"
    }
  }

  // Функция для получения названия интеграции
  const getIntegrationName = (integration: string) => {
    switch (integration) {
      case "signalwire":
        return "SignalWire"
      case "stripe":
        return "Stripe"
      case "clover":
        return "Clover"
      case "square":
        return "Square"
      case "mailchimp":
        return "Mailchimp"
      case "zendesk":
        return "Zendesk"
      case "hubspot":
        return "HubSpot"
      case "salesforce":
        return "Salesforce"
      case "shopify":
        return "Shopify"
      case "google-analytics":
        return "Google Analytics"
      case "intercom":
        return "Intercom"
      case "calendly":
        return "Calendly"
      default:
        return integration
    }
  }

  // Функция для получения описания интеграции
  const getIntegrationDescription = (integration: string) => {
    switch (integration) {
      case "signalwire":
        return "Платформа для унифицированных коммуникаций (PUC) с API для голоса, видео, сообщений и AI"
      case "stripe":
        return "Платежная система для онлайн-бизнеса с поддержкой карт, кошельков и локальных методов оплаты"
      case "clover":
        return "Платежная система для физических и онлайн-продаж с поддержкой POS-терминалов"
      case "square":
        return "Платежная система для малого и среднего бизнеса с поддержкой POS и онлайн-платежей"
      case "mailchimp":
        return "Платформа для email-маркетинга и автоматизации"
      case "zendesk":
        return "Платформа для поддержки клиентов и управления тикетами"
      case "hubspot":
        return "CRM-система для маркетинга, продаж и поддержки клиентов"
      case "salesforce":
        return "CRM-система для управления взаимоотношениями с клиентами"
      case "shopify":
        return "Платформа для создания и управления интернет-магазинами"
      case "google-analytics":
        return "Сервис для анализа посещаемости веб-сайтов и мобильных приложений"
      case "intercom":
        return "Платформа для общения с клиентами и поддержки"
      case "calendly":
        return "Сервис для планирования встреч и управления календарем"
      default:
        return ""
    }
  }

  // Функция для получения категории интеграции
  const getIntegrationCategory = (integration: string) => {
    switch (integration) {
      case "signalwire":
        return "communication"
      case "stripe":
      case "clover":
      case "square":
        return "payment"
      case "mailchimp":
        return "marketing"
      case "zendesk":
      case "intercom":
        return "support"
      case "hubspot":
      case "salesforce":
        return "crm"
      case "shopify":
        return "ecommerce"
      case "google-analytics":
        return "analytics"
      case "calendly":
        return "productivity"
      default:
        return "other"
    }
  }

  // Функция для получения названия категории
  const getCategoryName = (category: string) => {
    switch (category) {
      case "payment":
        return "Платежи"
      case "communication":
        return "Коммуникации"
      case "crm":
        return "CRM"
      case "analytics":
        return "Аналитика"
      case "logistics":
        return "Логистика"
      case "ecommerce":
        return "Электронная коммерция"
      case "marketing":
        return "Маркетинг"
      case "support":
        return "Поддержка"
      case "productivity":
        return "Продуктивность"
      case "all":
        return "Все категории"
      default:
        return "Другое"
    }
  }

  // Функция для получения иконки категории
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "payment":
        return <CreditCardIcon className="h-5 w-5" />
      case "communication":
        return <Phone className="h-5 w-5" />
      case "crm":
        return <Users className="h-5 w-5" />
      case "analytics":
        return <BarChart className="h-5 w-5" />
      case "logistics":
        return <Truck className="h-5 w-5" />
      case "ecommerce":
        return <ShoppingCart className="h-5 w-5" />
      case "marketing":
        return <Mail className="h-5 w-5" />
      case "support":
        return <Headphones className="h-5 w-5" />
      case "productivity":
        return <Calendar className="h-5 w-5" />
      case "all":
        return <Globe className="h-5 w-5" />
      default:
        return <Settings className="h-5 w-5" />
    }
  }

  // Функция для получения категорий настроек интеграции
  const getIntegrationCategories = (integration: string) => {
    switch (integration) {
      case "signalwire":
        return [
          { id: "general", name: "Основные настройки", icon: <Settings className="h-4 w-4" /> },
          { id: "voice", name: "Голос", icon: <Phone className="h-4 w-4" /> },
          { id: "video", name: "Видео", icon: <Video className="h-4 w-4" /> },
          { id: "messaging", name: "Сообщения", icon: <MessageSquare className="h-4 w-4" /> },
          { id: "ai", name: "AI", icon: <BrainCircuit className="h-4 w-4" /> },
          { id: "webhooks", name: "Вебхуки", icon: <Webhook className="h-4 w-4" /> },
        ]
      case "stripe":
        return [
          { id: "general", name: "Основные настройки", icon: <Settings className="h-4 w-4" /> },
          { id: "payment-methods", name: "Методы оплаты", icon: <Wallet className="h-4 w-4" /> },
          { id: "subscriptions", name: "Подписки", icon: <Receipt className="h-4 w-4" /> },
          { id: "invoices", name: "Счета", icon: <FileText className="h-4 w-4" /> },
          { id: "webhooks", name: "Вебхуки", icon: <Webhook className="h-4 w-4" /> },
        ]
      case "clover":
        return [
          { id: "general", name: "Основные настройки", icon: <Settings className="h-4 w-4" /> },
          { id: "terminals", name: "Терминалы", icon: <SquareTerminal className="h-4 w-4" /> },
          { id: "online", name: "Онлайн-платежи", icon: <ShoppingCart className="h-4 w-4" /> },
          { id: "inventory", name: "Инвентарь", icon: <Package className="h-4 w-4" /> },
          { id: "reporting", name: "Отчетность", icon: <BarChart3 className="h-4 w-4" /> },
        ]
      case "square":
        return [
          { id: "general", name: "Основные настройки", icon: <Settings className="h-4 w-4" /> },
          { id: "payment", name: "Платежи", icon: <CreditCardIcon className="h-4 w-4" /> },
          { id: "catalog", name: "Каталог", icon: <Package className="h-4 w-4" /> },
          { id: "customers", name: "Клиенты", icon: <Users className="h-4 w-4" /> },
          { id: "invoices", name: "Счета", icon: <FileText className="h-4 w-4" /> },
        ]
      default:
        return [{ id: "general", name: "Основные настройки", icon: <Settings className="h-4 w-4" /> }]
    }
  }

  // Список всех интеграций
  const allIntegrations = [
    { id: "signalwire", name: "SignalWire", category: "communication" },
    { id: "stripe", name: "Stripe", category: "payment" },
    { id: "clover", name: "Clover", category: "payment" },
    { id: "square", name: "Square", category: "payment" },
    { id: "mailchimp", name: "Mailchimp", category: "marketing" },
    { id: "zendesk", name: "Zendesk", category: "support" },
    { id: "hubspot", name: "HubSpot", category: "crm" },
    { id: "salesforce", name: "Salesforce", category: "crm" },
    { id: "shopify", name: "Shopify", category: "ecommerce" },
    { id: "google-analytics", name: "Google Analytics", category: "analytics" },
    { id: "intercom", name: "Intercom", category: "support" },
    { id: "calendly", name: "Calendly", category: "productivity" },
  ]

  // Список всех категорий
  const allCategories = [
    { id: "all", name: "Все категории" },
    { id: "payment", name: "Платежи" },
    { id: "communication", name: "Коммуникации" },
    { id: "crm", name: "CRM" },
    { id: "analytics", name: "Аналитика" },
    { id: "ecommerce", name: "Электронная коммерция" },
    { id: "marketing", name: "Маркетинг" },
    { id: "support", name: "Поддержка" },
    { id: "productivity", name: "Продуктивность" },
  ]

  // Фильтрация интеграций по категории
  const filteredIntegrations =
    activeCategory === "all"
      ? allIntegrations
      : allIntegrations.filter((integration) => integration.category === activeCategory)

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Интеграции</h1>
        <p className="text-muted-foreground">Управляйте интеграциями с внешними сервисами и системами</p>
      </div>

      <Tabs defaultValue="integrations" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="integrations">Интеграции</TabsTrigger>
          <TabsTrigger value="events">Журнал событий</TabsTrigger>
        </TabsList>

        {/* Вкладка интеграций */}
        <TabsContent value="integrations">
          {/* Фильтр категорий */}
          <div className="mb-6">
            <div className="flex flex-wrap gap-2">
              {allCategories.map((category) => (
                <Button
                  key={category.id}
                  variant={activeCategory === category.id ? "default" : "outline"}
                  size="sm"
                  className="flex items-center gap-2"
                  onClick={() => setActiveCategory(category.id)}
                >
                  {getCategoryIcon(category.id)}
                  <span>{getCategoryName(category.id)}</span>
                </Button>
              ))}
            </div>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {/* Карточки интеграций */}
            {filteredIntegrations.map((integration) => (
              <Card
                key={integration.id}
                className={`overflow-hidden transition-all duration-200 ${activeIntegration === integration.id ? "ring-2 ring-blue-500" : ""}`}
              >
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div
                        className={`flex h-10 w-10 items-center justify-center rounded-full ${getIntegrationColor(integration.id)}`}
                      >
                        {getIntegrationIcon(integration.id)}
                      </div>
                      <div>
                        <CardTitle className="text-xl">{getIntegrationName(integration.id)}</CardTitle>
                        <CardDescription className="line-clamp-1">
                          {getIntegrationDescription(integration.id)}
                        </CardDescription>
                      </div>
                    </div>
                    <Badge
                      variant={
                        integrationStatus[integration.id as keyof typeof integrationStatus] ? "success" : "outline"
                      }
                      className={
                        integrationStatus[integration.id as keyof typeof integrationStatus]
                          ? "bg-green-100 text-green-800"
                          : ""
                      }
                    >
                      {integrationStatus[integration.id as keyof typeof integrationStatus]
                        ? "Подключено"
                        : "Не подключено"}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="pb-3">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Категории</span>
                      <span className="text-sm text-muted-foreground">
                        {getIntegrationCategories(integration.id).length}
                      </span>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {getIntegrationCategories(integration.id)
                        .slice(0, 3)
                        .map((category) => (
                          <Badge key={category.id} variant="outline" className="flex items-center gap-1">
                            {category.icon}
                            <span>{category.name}</span>
                          </Badge>
                        ))}
                      {getIntegrationCategories(integration.id).length > 3 && (
                        <Badge variant="outline">+{getIntegrationCategories(integration.id).length - 3}</Badge>
                      )}
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between pt-0">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleTestConnection(integration.id)}
                    disabled={integrationStatus[integration.id as keyof typeof integrationStatus]}
                  >
                    {integrationStatus[integration.id as keyof typeof integrationStatus] ? (
                      <>
                        <Check className="mr-2 h-4 w-4" />
                        Подключено
                      </>
                    ) : (
                      "Тестировать"
                    )}
                  </Button>
                  <Button
                    size="sm"
                    onClick={() => setActiveIntegration(activeIntegration === integration.id ? null : integration.id)}
                  >
                    {activeIntegration === integration.id ? "Закрыть" : "Настроить"}
                  </Button>
                </CardFooter>
              </Card>
            ))}

            {/* Карточка добавления новой интеграции */}
            <Card className="border-dashed">
              <CardContent className="flex flex-col items-center justify-center p-6">
                <div className="mb-4 rounded-full bg-muted p-3">
                  <Plus className="h-6 w-6" />
                </div>
                <h3 className="mb-1 text-lg font-medium">Добавить интеграцию</h3>
                <p className="mb-4 text-center text-sm text-muted-foreground">
                  Подключите новый сервис или систему к вашему приложению
                </p>
                <Button>Добавить</Button>
              </CardContent>
            </Card>
          </div>

          {/* Детальные настройки активной интеграции */}
          {activeIntegration && (
            <Card className="mt-6">
              <CardHeader>
                <div className="flex items-center gap-4">
                  <div
                    className={`flex h-12 w-12 items-center justify-center rounded-full ${getIntegrationColor(activeIntegration)}`}
                  >
                    {getIntegrationIcon(activeIntegration)}
                  </div>
                  <div>
                    <CardTitle>{getIntegrationName(activeIntegration)}</CardTitle>
                    <CardDescription>{getIntegrationDescription(activeIntegration)}</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {/* API Credentials */}
                <div className="mb-6 rounded-lg border p-4">
                  <h3 className="mb-4 text-lg font-medium">API Credentials</h3>
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor={`${activeIntegration}-api-key`}>API Key</Label>
                      <Input
                        id={`${activeIntegration}-api-key`}
                        type="password"
                        value={apiCredentials[activeIntegration]?.apiKey || ""}
                        onChange={(e) => handleApiCredentialsChange(activeIntegration, "apiKey", e.target.value)}
                        placeholder="Введите API ключ"
                      />
                      <p className="text-sm text-muted-foreground">
                        API ключ можно найти в настройках вашего аккаунта {getIntegrationName(activeIntegration)}
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor={`${activeIntegration}-api-secret`}>API Secret</Label>
                      <Input
                        id={`${activeIntegration}-api-secret`}
                        type="password"
                        value={apiCredentials[activeIntegration]?.apiSecret || ""}
                        onChange={(e) => handleApiCredentialsChange(activeIntegration, "apiSecret", e.target.value)}
                        placeholder="Введите API Secret"
                      />
                      <p className="text-sm text-muted-foreground">API Secret используется для подписи запросов</p>
                    </div>

                    {activeIntegration === "signalwire" && (
                      <div className="space-y-2">
                        <Label htmlFor="space-url">Space URL</Label>
                        <Input
                          id="space-url"
                          placeholder="your-space.signalwire.com"
                          value={signalWireSettings.spaceUrl}
                          onChange={(e) => handleSignalWireSettingsChange("spaceUrl", e.target.value)}
                        />
                      </div>
                    )}

                    <div className="space-y-2">
                      <Label htmlFor={`${activeIntegration}-environment`}>Окружение</Label>
                      <Select
                        value={apiCredentials[activeIntegration]?.environment || "production"}
                        onValueChange={(value) => handleApiCredentialsChange(activeIntegration, "environment", value)}
                      >
                        <SelectTrigger id={`${activeIntegration}-environment`}>
                          <SelectValue placeholder="Выберите окружение" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="production">Production</SelectItem>
                          <SelectItem value="sandbox">Sandbox</SelectItem>
                          <SelectItem value="development">Development</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>

                <Accordion type="single" collapsible className="w-full">
                  {getIntegrationCategories(activeIntegration).map((category) => (
                    <AccordionItem key={category.id} value={category.id}>
                      <AccordionTrigger className="flex items-center gap-2">
                        {category.icon}
                        <span>{category.name}</span>
                      </AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-4 pt-2">
                          {category.id === "general" && (
                            <>
                              {activeIntegration === "stripe" && (
                                <>
                                  <div className="space-y-2">
                                    <Label htmlFor="publishable-key">Publishable Key</Label>
                                    <Input
                                      id="publishable-key"
                                      placeholder="pk_live_..."
                                      value={stripeSettings.publishableKey}
                                      onChange={(e) =>
                                        setStripeSettings({ ...stripeSettings, publishableKey: e.target.value })
                                      }
                                    />
                                  </div>
                                  <div className="space-y-2">
                                    <div className="flex items-center space-x-2">
                                      <Switch
                                        id="test-mode"
                                        checked={stripeSettings.testMode}
                                        onCheckedChange={(checked) =>
                                          setStripeSettings({ ...stripeSettings, testMode: checked })
                                        }
                                      />
                                      <Label htmlFor="test-mode">Тестовый режим</Label>
                                    </div>
                                  </div>
                                </>
                              )}

                              {activeIntegration === "clover" && (
                                <>
                                  <div className="space-y-2">
                                    <Label htmlFor="merchant-id">Merchant ID</Label>
                                    <Input
                                      id="merchant-id"
                                      placeholder="Введите Merchant ID"
                                      value={cloverSettings.merchantId}
                                      onChange={(e) =>
                                        setCloverSettings({ ...cloverSettings, merchantId: e.target.value })
                                      }
                                    />
                                  </div>
                                </>
                              )}

                              {activeIntegration === "square" && (
                                <>
                                  <div className="space-y-2">
                                    <Label htmlFor="location-id">Location ID</Label>
                                    <Input
                                      id="location-id"
                                      placeholder="Введите Location ID"
                                      value={squareSettings.locationId}
                                      onChange={(e) =>
                                        setSquareSettings({ ...squareSettings, locationId: e.target.value })
                                      }
                                    />
                                  </div>
                                </>
                              )}

                              <div className="rounded-md bg-blue-50 p-4">
                                <div className="flex">
                                  <div className="flex-shrink-0">
                                    <Info className="h-5 w-5 text-blue-400" aria-hidden="true" />
                                  </div>
                                  <div className="ml-3">
                                    <h3 className="text-sm font-medium text-blue-800">
                                      О {getIntegrationName(activeIntegration)}
                                    </h3>
                                    <div className="mt-2 text-sm text-blue-700">
                                      <p>{getIntegrationDescription(activeIntegration)}</p>
                                    </div>
                                    <div className="mt-4">
                                      <a href="#" className="text-sm font-medium text-blue-600 hover:text-blue-500">
                                        Узнать больше о {getIntegrationName(activeIntegration)} →
                                      </a>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </>
                          )}

                          {/* Здесь можно добавить содержимое для других категорий */}
                          {category.id !== "general" && (
                            <div className="flex h-40 items-center justify-center rounded-md border border-dashed">
                              <div className="text-center">
                                <h3 className="text-lg font-medium">{category.name}</h3>
                                <p className="text-sm text-muted-foreground">
                                  Настройки для {category.name.toLowerCase()} будут доступны после подключения
                                </p>
                              </div>
                            </div>
                          )}
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={() => setActiveIntegration(null)}>
                  Отмена
                </Button>
                <Button onClick={() => setShowSaveConfirmDialog(true)} disabled={isSaving}>
                  {isSaving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Сохранение...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Сохранить настройки
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>
          )}
        </TabsContent>

        {/* Вкладка журнала событий */}
        <TabsContent value="events">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Журнал событий интеграций</CardTitle>
                <Button variant="outline" size="sm" onClick={handleExportEvents}>
                  <Download className="mr-2 h-4 w-4" />
                  Экспорт
                </Button>
              </div>
              <CardDescription>Отслеживайте события, предупреждения и ошибки, связанные с интеграциями</CardDescription>
            </CardHeader>
            <CardContent>
              {/* Фильтры и поиск */}
              <div className="mb-4 flex flex-wrap items-center gap-4">
                <div className="flex items-center gap-2">
                  <Label htmlFor="event-type-filter" className="sr-only">
                    Тип события
                  </Label>
                  <Select value={eventFilters.type} onValueChange={handleEventTypeFilterChange}>
                    <SelectTrigger id="event-type-filter" className="w-[140px]">
                      <SelectValue placeholder="Тип события" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Все типы</SelectItem>
                      <SelectItem value="info">Информация</SelectItem>
                      <SelectItem value="warning">Предупреждения</SelectItem>
                      <SelectItem value="error">Ошибки</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center gap-2">
                  <Label htmlFor="event-source-filter" className="sr-only">
                    Источник
                  </Label>
                  <Select value={eventFilters.source} onValueChange={handleEventSourceFilterChange}>
                    <SelectTrigger id="event-source-filter" className="w-[160px]">
                      <SelectValue placeholder="Источник" />
                    </SelectTrigger>
                    <SelectContent>
                      {uniqueSources.map((source) => (
                        <SelectItem key={source} value={source}>
                          {source === "all" ? "Все источники" : source}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center gap-2">
                  <Label htmlFor="event-resolved-filter" className="sr-only">
                    Статус
                  </Label>
                  <Select value={eventFilters.resolved} onValueChange={handleEventResolvedFilterChange}>
                    <SelectTrigger id="event-resolved-filter" className="w-[140px]">
                      <SelectValue placeholder="Статус" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Все статусы</SelectItem>
                      <SelectItem value="resolved">Разрешенные</SelectItem>
                      <SelectItem value="unresolved">Неразрешенные</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                    <Input
                      placeholder="Поиск по сообщениям и деталям..."
                      value={eventFilters.search}
                      onChange={(e) => handleSearchChange(e.target.value)}
                      className="pl-8"
                    />
                  </div>
                </div>
              </div>

              {/* Таблица событий */}
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[100px]">Тип</TableHead>
                      <TableHead>Источник</TableHead>
                      <TableHead className="w-[300px]">Сообщение</TableHead>
                      <TableHead className="w-[180px]">Дата и время</TableHead>
                      <TableHead className="w-[100px]">Статус</TableHead>
                      <TableHead className="w-[100px] text-right">Действия</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredEvents.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={6} className="h-24 text-center">
                          Нет событий, соответствующих заданным критериям
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredEvents.map((event) => (
                        <TableRow key={event.id}>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              {getEventTypeIcon(event.type)}
                              <Badge className={getEventTypeBadgeColor(event.type)}>
                                {getEventTypeName(event.type)}
                              </Badge>
                            </div>
                          </TableCell>
                          <TableCell>{event.source}</TableCell>
                          <TableCell className="font-medium">{event.message}</TableCell>
                          <TableCell>{formatDateTime(event.timestamp)}</TableCell>
                          <TableCell>
                            {event.resolved ? (
                              <Badge variant="outline" className="bg-green-50 text-green-600">
                                Разрешено
                              </Badge>
                            ) : (
                              <Badge variant="outline" className="bg-red-50 text-red-600">
                                Не разрешено
                              </Badge>
                            )}
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon">
                                  <ChevronDown className="h-4 w-4" />
                                  <span className="sr-only">Действия</span>
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() => handleViewEventDetails(event)}>
                                  <Eye className="mr-2 h-4 w-4" />
                                  Просмотр деталей
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleToggleEventResolved(event.id)}>
                                  {event.resolved ? (
                                    <>
                                      <AlertCircle className="mr-2 h-4 w-4" />
                                      Отметить как неразрешенное
                                    </>
                                  ) : (
                                    <>
                                      <Check className="mr-2 h-4 w-4" />
                                      Отметить как разрешенное
                                    </>
                                  )}
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Диалог с деталями события */}
      <Dialog open={selectedEvent !== null} onOpenChange={(open) => !open && handleCloseEventDetails()}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Детали события</DialogTitle>
            <DialogDescription>Подробная информация о событии интеграции {selectedEvent?.source}</DialogDescription>
          </DialogHeader>
          {selectedEvent && (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                {getEventTypeIcon(selectedEvent.type)}
                <Badge className={getEventTypeBadgeColor(selectedEvent.type)}>
                  {getEventTypeName(selectedEvent.type)}
                </Badge>
              </div>

              <div>
                <h4 className="mb-1 text-sm font-medium">Источник</h4>
                <p>{selectedEvent.source}</p>
              </div>

              <div>
                <h4 className="mb-1 text-sm font-medium">Сообщение</h4>
                <p>{selectedEvent.message}</p>
              </div>

              <div>
                <h4 className="mb-1 text-sm font-medium">Дата и время</h4>
                <p>{formatDateTime(selectedEvent.timestamp)}</p>
              </div>

              <div>
                <h4 className="mb-1 text-sm font-medium">Статус</h4>
                <p>
                  {selectedEvent.resolved ? (
                    <Badge variant="outline" className="bg-green-50 text-green-600">
                      Разрешено
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="bg-red-50 text-red-600">
                      Не разрешено
                    </Badge>
                  )}
                </p>
              </div>

              {selectedEvent.details && (
                <div>
                  <h4 className="mb-1 text-sm font-medium">Детали</h4>
                  <p className="whitespace-pre-wrap rounded-md bg-muted p-2 text-sm">{selectedEvent.details}</p>
                </div>
              )}

              <div className="flex justify-between">
                <Button
                  variant="outline"
                  onClick={() => {
                    handleToggleEventResolved(selectedEvent.id)
                    handleCloseEventDetails()
                  }}
                >
                  {selectedEvent.resolved ? "Отметить как неразрешенное" : "Отметить как разрешенное"}
                </Button>
                <Button onClick={handleCloseEventDetails}>Закрыть</Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Диалог подтверждения сохранения */}
      <Dialog open={showSaveConfirmDialog} onOpenChange={setShowSaveConfirmDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Сохранить настройки</DialogTitle>
            <DialogDescription>
              Вы уверены, что хотите сохранить настройки интеграции {getIntegrationName(activeIntegration || "")}?
            </DialogDescription>
          </DialogHeader>
          <div className="mt-4 flex justify-end space-x-2">
            <Button variant="outline" onClick={() => setShowSaveConfirmDialog(false)}>
              Отмена
            </Button>
            <Button
              onClick={() => {
                setShowSaveConfirmDialog(false)
                handleSaveIntegrationSettings()
              }}
            >
              Сохранить
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Диалог добавления телефонного номера */}
      <Dialog open={showAddPhoneNumberDialog} onOpenChange={setShowAddPhoneNumberDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Добавить телефонный номер</DialogTitle>
            <DialogDescription>Добавьте новый телефонный номер для использования в SignalWire</DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="new-phone-number">Номер телефона</Label>
              <Input id="new-phone-number" placeholder="+7XXXXXXXXXX" />
            </div>

            <div className="space-y-2">
              <Label htmlFor="new-phone-type">Тип номера</Label>
              <Select defaultValue="local">
                <SelectTrigger id="new-phone-type">
                  <SelectValue placeholder="Выберите тип номера" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="local">Местный</SelectItem>
                  <SelectItem value="tollfree">Бесплатный</SelectItem>
                  <SelectItem value="international">Международный</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Возможности</Label>
              <div className="flex flex-wrap gap-4">
                <div className="flex items-center space-x-2">
                  <Checkbox id="capability-voice" />
                  <Label htmlFor="capability-voice">Голос</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="capability-sms" />
                  <Label htmlFor="capability-sms">SMS</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="capability-fax" />
                  <Label htmlFor="capability-fax">Факс</Label>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox id="is-default" />
              <Label htmlFor="is-default">Использовать по умолчанию</Label>
            </div>

            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setShowAddPhoneNumberDialog(false)}>
                Отмена
              </Button>
              <Button
                onClick={() => {
                  const newPhoneNumber: PhoneNumber = {
                    id: generateId(),
                    number: (document.getElementById("new-phone-number") as HTMLInputElement).value,
                    type: (document.getElementById("new-phone-type") as HTMLSelectElement).value as
                      | "local"
                      | "tollfree"
                      | "international",
                    capabilities: [
                      ...((document.getElementById("capability-voice") as HTMLInputElement).checked ? ["voice"] : []),
                      ...((document.getElementById("capability-sms") as HTMLInputElement).checked ? ["sms"] : []),
                      ...((document.getElementById("capability-fax") as HTMLInputElement).checked ? ["fax"] : []),
                    ] as ("voice" | "sms" | "fax")[],
                    isDefault: (document.getElementById("is-default") as HTMLInputElement).checked,
                  }
                  handleAddPhoneNumber(newPhoneNumber)
                }}
              >
                Добавить
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
