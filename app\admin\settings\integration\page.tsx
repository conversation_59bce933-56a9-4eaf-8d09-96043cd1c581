"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import {
  <PERSON>tings,
  Check,
  X,
  AlertCircle,
  Loader2,
  ExternalLink,
  Copy,
  Eye,
  EyeOff,
  RefreshCw,
  Zap,
  Shield,
  Globe,
  CreditCard,
  MessageSquare,
  Phone,
  Mail,
  BarChart3,
  Package,
  Truck,
  Users,
  Calendar,
  FileText,
  Webhook,
  Key,
  Database,
  Cloud,
  Lock,
  Plus,
  Wallet
} from "lucide-react"
import { toast } from "@/hooks/use-toast"
import { IntegrationConfigDialog } from "./integration-config-dialog"

// Типы интеграций
interface Integration {
  id: string
  name: string
  description: string
  category: 'payment' | 'communication' | 'analytics' | 'crm' | 'ecommerce' | 'marketing' | 'support' | 'logistics'
  icon: React.ElementType
  status: 'connected' | 'disconnected' | 'error' | 'configuring'
  isPopular?: boolean
  isPremium?: boolean
  setupComplexity: 'easy' | 'medium' | 'advanced'
  features: string[]
  pricing?: string
  documentation?: string
  supportedRegions?: string[]
  lastSync?: string
  config?: Record<string, any>
}

// Категории интеграций
const categories = [
  { id: 'all', name: 'Все интеграции', icon: Globe },
  { id: 'payment', name: 'Платежи', icon: CreditCard },
  { id: 'communication', name: 'Коммуникации', icon: MessageSquare },
  { id: 'analytics', name: 'Аналитика', icon: BarChart3 },
  { id: 'crm', name: 'CRM', icon: Users },
  { id: 'ecommerce', name: 'E-commerce', icon: Package },
  { id: 'marketing', name: 'Маркетинг', icon: Mail },
  { id: 'support', name: 'Поддержка', icon: Phone },
  { id: 'logistics', name: 'Логистика', icon: Truck }
]

// Доступные интеграции
const availableIntegrations: Integration[] = [
  {
    id: 'stripe',
    name: 'Stripe',
    description: 'Полная платежная инфраструктура для интернет-бизнеса - от простых платежей до сложных финансовых продуктов',
    category: 'payment',
    icon: CreditCard,
    status: 'disconnected',
    isPopular: true,
    isPremium: true,
    setupComplexity: 'medium',
    features: [
      'Payments - Онлайн-платежи',
      'Terminal - Платежи в точках продаж',
      'Radar - Защита от мошенничества',
      'Authorization - Оптимизация авторизации',
      'Connect - Платежи для платформ',
      'Global Payouts - Международные выплаты',
      'Capital - Финансирование клиентов',
      'Issuing - Выпуск карт',
      'Billing - Подписки и биллинг',
      'Invoicing - Онлайн-счета',
      'Revenue Recognition - Учет доходов',
      'Sigma - Кастомные отчеты',
      'Tax - Автоматизация налогов',
      'Data Pipeline - Синхронизация данных',
      'Payment Links - Платежи без кода',
      'Checkout - Готовая форма оплаты',
      'Elements - UI компоненты',
      'Financial Connections - Банковские данные',
      'Identity - Верификация личности',
      'Atlas - Регистрация компаний',
      'Climate - Углеродная нейтральность',
      'Treasury - Банковские услуги'
    ],
    pricing: 'От 2.9% + 30¢ за транзакцию',
    documentation: 'https://stripe.com/docs',
    supportedRegions: ['US', 'EU', 'CA', 'AU', 'JP', 'SG', 'MX', 'BR', 'IN', 'MY', 'TH', 'PH', 'HK']
  },
  {
    id: 'paypal',
    name: 'PayPal',
    description: 'Полная экосистема цифровых платежей и финансовых услуг для бизнеса любого размера',
    category: 'payment',
    icon: Wallet,
    status: 'disconnected',
    isPopular: true,
    isPremium: true,
    setupComplexity: 'medium',
    features: [
      'PayPal Checkout - Быстрая оплата',
      'PayPal Pay Later - Отложенные платежи',
      'PayPal Credit - Кредитование',
      'Venmo - Социальные платежи',
      'PayPal Business - Бизнес-аккаунты',
      'PayPal Commerce Platform - Платформа для торговли',
      'PayPal Payments Pro - Продвинутые платежи',
      'PayPal Express Checkout - Экспресс-оплата',
      'PayPal Subscriptions - Подписки',
      'PayPal Invoicing - Выставление счетов',
      'PayPal Payouts - Массовые выплаты',
      'PayPal Marketplace - Торговые площадки',
      'PayPal Advanced Fraud Protection - Защита от мошенничества',
      'PayPal Dispute Resolution - Разрешение споров',
      'PayPal Multi-Party Payments - Многосторонние платежи',
      'PayPal QR Code Payments - QR-платежи',
      'PayPal Here - Мобильные платежи',
      'PayPal Working Capital - Бизнес-кредиты',
      'PayPal Seller Protection - Защита продавца',
      'PayPal Buyer Protection - Защита покупателя',
      'PayPal Analytics - Аналитика платежей',
      'PayPal Webhooks - Уведомления в реальном времени'
    ],
    pricing: 'От 2.9% + $0.30 за транзакцию',
    documentation: 'https://developer.paypal.com/docs',
    supportedRegions: ['US', 'EU', 'CA', 'AU', 'JP', 'GB', 'DE', 'FR', 'IT', 'ES', 'NL', 'BR', 'MX', 'IN', 'SG']
  },
  {
    id: 'signalwire',
    name: 'SignalWire',
    description: 'Полная облачная платформа для современных коммуникаций и программируемых медиа',
    category: 'communication',
    icon: Phone,
    status: 'connected',
    isPopular: true,
    isPremium: true,
    setupComplexity: 'medium',
    features: [
      'Voice API - Программируемые голосовые звонки',
      'Video API - Видеоконференции и стриминг',
      'Messaging API - SMS, MMS и чат',
      'WebRTC - Браузерные коммуникации',
      'SIP Trunking - Корпоративная телефония',
      'Call Flows - Визуальный конструктор звонков',
      'Relay - Реалтайм коммуникации',
      'Compatibility API - Совместимость с Twilio',
      'AI Voice - Голосовые ассистенты с ИИ',
      'Speech Recognition - Распознавание речи',
      'Text-to-Speech - Синтез речи',
      'Call Recording - Запись разговоров',
      'Call Analytics - Аналитика звонков',
      'Number Management - Управление номерами',
      'Elastic SIP Trunking - Масштабируемая SIP-связь',
      'Video Rooms - Комнаты для видеоконференций',
      'Live Streaming - Прямые трансляции',
      'Screen Sharing - Демонстрация экрана',
      'Chat API - Текстовые чаты',
      'Push Notifications - Push-уведомления',
      'Presence API - Статусы присутствия',
      'Call Queues - Очереди звонков',
      'IVR Builder - Интерактивное голосовое меню',
      'Webhooks - Уведомления в реальном времени'
    ],
    pricing: 'От $0.0085 за минуту голоса, $0.0075 за SMS',
    documentation: 'https://docs.signalwire.com',
    supportedRegions: ['US', 'CA', 'GB', 'AU', 'DE', 'FR', 'IT', 'ES', 'NL', 'SE', 'NO', 'DK', 'FI', 'IE', 'CH'],
    lastSync: '2 минуты назад'
  },
  {
    id: 'twilio',
    name: 'Twilio',
    description: 'Ведущая платформа коммуникаций для разработчиков с полным набором API и сервисов',
    category: 'communication',
    icon: MessageSquare,
    status: 'disconnected',
    isPopular: true,
    isPremium: true,
    setupComplexity: 'medium',
    features: [
      'SMS API - Программируемые SMS сообщения',
      'Voice API - Голосовые звонки и IVR',
      'Video API - Видеозвонки и конференции',
      'WhatsApp Business API - Бизнес-мессенджер',
      'Verify API - Двухфакторная аутентификация',
      'Conversations API - Омниканальные чаты',
      'SendGrid Email API - Доставка email',
      'Flex Contact Center - Контакт-центр',
      'Studio - Визуальный конструктор потоков',
      'Functions - Serverless вычисления',
      'Sync - Синхронизация состояния',
      'Chat API - Программируемые чаты',
      'Notify API - Push-уведомления',
      'Autopilot - AI-ассистенты и боты',
      'Programmable Wireless - IoT подключения',
      'Super Network - Глобальная инфраструктура',
      'Lookup API - Проверка номеров телефонов',
      'Proxy API - Анонимизация номеров',
      'TaskRouter - Маршрутизация задач',
      'Insights API - Аналитика качества звонков',
      'Marketplace - Дополнения и интеграции',
      'Console API - Программное управление',
      'Monitor API - Мониторинг и алерты',
      'Usage API - Отчеты по использованию'
    ],
    pricing: 'От $0.0075 за SMS, $0.0085 за минуту голоса',
    documentation: 'https://www.twilio.com/docs',
    supportedRegions: ['US', 'CA', 'GB', 'AU', 'DE', 'FR', 'IT', 'ES', 'NL', 'SE', 'JP', 'SG', 'BR', 'MX', 'IN']
  },
  {
    id: 'google-analytics',
    name: 'Google Analytics',
    description: 'Полная платформа веб-аналитики и маркетинговой аналитики от Google',
    category: 'analytics',
    icon: BarChart3,
    status: 'connected',
    isPopular: true,
    isPremium: true,
    setupComplexity: 'medium',
    features: [
      'Google Analytics 4 - Новое поколение аналитики',
      'Universal Analytics - Классическая аналитика',
      'Enhanced E-commerce - Расширенная электронная торговля',
      'Google Tag Manager - Управление тегами',
      'Measurement Protocol - Серверная аналитика',
      'Data Studio Integration - Интеграция с отчетами',
      'BigQuery Export - Экспорт в BigQuery',
      'Attribution Modeling - Модели атрибуции',
      'Audience Builder - Создание аудиторий',
      'Custom Dimensions - Пользовательские измерения',
      'Custom Metrics - Пользовательские метрики',
      'Goal Tracking - Отслеживание целей',
      'Event Tracking - Отслеживание событий',
      'Cross-Domain Tracking - Межсайтовое отслеживание',
      'User-ID Tracking - Отслеживание пользователей',
      'Demographics & Interests - Демография и интересы',
      'Real-Time Analytics - Аналитика в реальном времени',
      'Cohort Analysis - Когортный анализ',
      'Funnel Analysis - Анализ воронок',
      'Path Analysis - Анализ путей пользователей',
      'Intelligence Insights - Умные инсайты',
      'Data Import - Импорт данных',
      'Reporting API - API отчетов',
      'Admin API - API администрирования'
    ],
    pricing: 'Бесплатно до 10M событий/месяц, Analytics 360 от $150k/год',
    documentation: 'https://developers.google.com/analytics',
    supportedRegions: ['Global', 'US', 'EU', 'CA', 'AU', 'JP', 'GB', 'DE', 'FR', 'IT', 'ES', 'NL', 'BR', 'MX', 'IN'],
    lastSync: '1 час назад'
  },
  {
    id: 'augment-code',
    name: 'Augment Code',
    description: 'AI-платформа для разработки с лучшим в мире контекстным движком кода',
    category: 'developer-tools',
    icon: Code2,
    status: 'disconnected',
    isPopular: true,
    isPremium: true,
    setupComplexity: 'easy',
    features: [
      'Augment Agent - AI-ассистент для разработки',
      'Context Engine - Лучший в мире контекстный движок',
      'Code Generation - Генерация кода с ИИ',
      'Code Review - Автоматический ревью кода',
      'Bug Detection - Обнаружение багов и уязвимостей',
      'Code Refactoring - Рефакторинг с ИИ',
      'Documentation Generation - Автогенерация документации',
      'Test Generation - Автоматическое создание тестов',
      'Code Completion - Умное автодополнение',
      'Multi-Language Support - Поддержка множества языков',
      'IDE Integration - Интеграция с IDE',
      'Git Integration - Интеграция с Git',
      'Team Collaboration - Командная работа',
      'Code Analytics - Аналитика кода',
      'Performance Optimization - Оптимизация производительности',
      'Security Scanning - Сканирование безопасности',
      'API Integration - Интеграция через API',
      'Custom Models - Пользовательские модели ИИ',
      'Workflow Automation - Автоматизация рабочих процессов',
      'Real-time Collaboration - Совместная работа в реальном времени',
      'Code Search - Поиск по коду',
      'Dependency Management - Управление зависимостями',
      'CI/CD Integration - Интеграция с CI/CD',
      'Enterprise Security - Корпоративная безопасность'
    ],
    pricing: 'Бесплатно для индивидуальных разработчиков, Team от $20/мес, Enterprise по запросу',
    documentation: 'https://docs.augmentcode.com',
    supportedRegions: ['Global', 'US', 'EU', 'CA', 'AU', 'JP', 'GB', 'DE', 'FR', 'IT', 'ES', 'NL', 'BR', 'MX', 'IN']
  },
  {
    id: 'hubspot',
    name: 'HubSpot',
    description: 'CRM и маркетинговая автоматизация',
    category: 'crm',
    icon: Users,
    status: 'disconnected',
    setupComplexity: 'medium',
    features: ['CRM', 'Email маркетинг', 'Lead scoring', 'Автоматизация'],
    pricing: 'От $45/месяц'
  },
  {
    id: 'shopify',
    name: 'Shopify',
    description: 'Платформа для создания интернет-магазинов',
    category: 'ecommerce',
    icon: Package,
    status: 'disconnected',
    isPopular: true,
    setupComplexity: 'easy',
    features: ['Управление товарами', 'Заказы', 'Клиенты', 'Отчеты'],
    pricing: 'От $29/месяц'
  },
  {
    id: 'mailchimp',
    name: 'Mailchimp',
    description: 'Email маркетинг и автоматизация',
    category: 'marketing',
    icon: Mail,
    status: 'disconnected',
    setupComplexity: 'easy',
    features: ['Email кампании', 'Автоматизация', 'A/B тестирование', 'Аналитика'],
    pricing: 'От $10/месяц'
  },
  {
    id: 'zendesk',
    name: 'Zendesk',
    description: 'Платформа для службы поддержки клиентов',
    category: 'support',
    icon: Phone,
    status: 'error',
    setupComplexity: 'medium',
    features: ['Тикет-система', 'Live chat', 'Knowledge base', 'Аналитика'],
    pricing: 'От $19/месяц'
  },
  {
    id: 'fedex',
    name: 'FedEx',
    description: 'Служба доставки и логистики',
    category: 'logistics',
    icon: Truck,
    status: 'disconnected',
    setupComplexity: 'advanced',
    features: ['Отслеживание посылок', 'Расчет стоимости', 'Печать этикеток'],
    pricing: 'По тарифам FedEx'
  }
]

export default function ImprovedIntegrationPage() {
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [integrations, setIntegrations] = useState<Integration[]>(availableIntegrations)
  const [selectedIntegration, setSelectedIntegration] = useState<Integration | null>(null)
  const [showConfigDialog, setShowConfigDialog] = useState(false)
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [isConnecting, setIsConnecting] = useState(false)
  const [showApiKey, setShowApiKey] = useState(false)

  // Состояние для новой интеграции
  const [newIntegration, setNewIntegration] = useState({
    name: '',
    description: '',
    category: 'payment' as Integration['category'],
    apiUrl: '',
    apiKey: '',
    webhookUrl: ''
  })

  // Фильтрация интеграций
  const filteredIntegrations = integrations.filter(integration => {
    const matchesCategory = selectedCategory === 'all' || integration.category === selectedCategory
    const matchesSearch = integration.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         integration.description.toLowerCase().includes(searchQuery.toLowerCase())
    return matchesCategory && matchesSearch
  })

  // Статистика
  const stats = {
    total: integrations.length,
    connected: integrations.filter(i => i.status === 'connected').length,
    errors: integrations.filter(i => i.status === 'error').length
  }

  // Получение иконки статуса
  const getStatusIcon = (status: Integration['status']) => {
    switch (status) {
      case 'connected': return <Check className="h-4 w-4 text-green-500" />
      case 'error': return <X className="h-4 w-4 text-red-500" />
      case 'configuring': return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />
      default: return <AlertCircle className="h-4 w-4 text-gray-400" />
    }
  }

  // Получение цвета бейджа статуса
  const getStatusBadgeVariant = (status: Integration['status']) => {
    switch (status) {
      case 'connected': return 'success' as const
      case 'error': return 'destructive' as const
      case 'configuring': return 'secondary' as const
      default: return 'outline' as const
    }
  }

  // Получение текста статуса
  const getStatusText = (status: Integration['status']) => {
    switch (status) {
      case 'connected': return 'Подключено'
      case 'error': return 'Ошибка'
      case 'configuring': return 'Настройка'
      default: return 'Не подключено'
    }
  }

  // Подключение интеграции
  const handleConnect = async (integration: Integration) => {
    setIsConnecting(true)

    // Симуляция подключения
    await new Promise(resolve => setTimeout(resolve, 2000))

    setIntegrations(prev => prev.map(i =>
      i.id === integration.id
        ? {
            ...i,
            status: 'connected' as const,
            lastSync: 'Только что',
            config: integration.config // Сохраняем конфигурацию
          }
        : i
    ))

    setIsConnecting(false)
    setShowConfigDialog(false)

    toast({
      title: "Интеграция подключена",
      description: `${integration.name} успешно подключен к вашей системе.`
    })
  }

  // Отключение интеграции
  const handleDisconnect = async (integration: Integration) => {
    setIntegrations(prev => prev.map(i =>
      i.id === integration.id
        ? { ...i, status: 'disconnected' as const, lastSync: undefined }
        : i
    ))

    toast({
      title: "Интеграция отключена",
      description: `${integration.name} отключен от вашей системы.`
    })
  }

  // Добавление новой интеграции
  const handleAddIntegration = () => {
    if (!newIntegration.name || !newIntegration.description) {
      toast({
        title: "Ошибка",
        description: "Пожалуйста, заполните все обязательные поля."
      })
      return
    }

    const integration: Integration = {
      id: `custom-${Date.now()}`,
      name: newIntegration.name,
      description: newIntegration.description,
      category: newIntegration.category,
      icon: Database, // Иконка по умолчанию для пользовательских интеграций
      status: 'disconnected',
      setupComplexity: 'advanced',
      features: ['Custom API', 'Webhook поддержка', 'Настраиваемые поля'],
      pricing: 'Настраивается',
      config: {
        apiUrl: newIntegration.apiUrl,
        apiKey: newIntegration.apiKey,
        webhookUrl: newIntegration.webhookUrl
      }
    }

    setIntegrations(prev => [...prev, integration])
    setNewIntegration({
      name: '',
      description: '',
      category: 'payment',
      apiUrl: '',
      apiKey: '',
      webhookUrl: ''
    })
    setShowAddDialog(false)

    toast({
      title: "Интеграция добавлена",
      description: `${integration.name} успешно добавлена в список интеграций.`
    })
  }

  return (
    <div className="space-y-6">
      {/* Заголовок */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Интеграции</h1>
          <p className="text-muted-foreground mt-2">
            Управляйте интеграциями с внешними сервисами и системами
          </p>
        </div>
        <Button onClick={() => setShowAddDialog(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Добавить интеграцию
        </Button>
      </div>

      {/* Статистика */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Всего интеграций</CardTitle>
            <Globe className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Подключено</CardTitle>
            <Check className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.connected}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ошибки</CardTitle>
            <AlertCircle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.errors}</div>
          </CardContent>
        </Card>
      </div>

      {/* Фильтры и поиск */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <Input
            placeholder="Поиск интеграций..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="max-w-sm"
          />
        </div>
        <Tabs value={selectedCategory} onValueChange={setSelectedCategory} className="w-full sm:w-auto">
          <TabsList className="grid w-full grid-cols-3 sm:grid-cols-9">
            {categories.map((category) => {
              const Icon = category.icon
              return (
                <TabsTrigger key={category.id} value={category.id} className="flex items-center gap-1">
                  <Icon className="h-4 w-4" />
                  <span className="hidden sm:inline">{category.name}</span>
                </TabsTrigger>
              )
            })}
          </TabsList>
        </Tabs>
      </div>

      {/* Список интеграций */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {filteredIntegrations.map((integration) => {
          const Icon = integration.icon
          return (
            <Card key={integration.id} className="relative">
              {integration.isPopular && (
                <Badge className="absolute -top-2 -right-2 bg-orange-500">
                  Популярное
                </Badge>
              )}
              {integration.isPremium && (
                <Badge className="absolute -top-2 -right-2 bg-purple-500">
                  Premium
                </Badge>
              )}

              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-muted rounded-lg">
                      <Icon className="h-6 w-6" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{integration.name}</CardTitle>
                      <div className="flex items-center gap-2 mt-1">
                        {getStatusIcon(integration.status)}
                        <Badge variant={getStatusBadgeVariant(integration.status)}>
                          {getStatusText(integration.status)}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
                <CardDescription className="mt-2">
                  {integration.description}
                </CardDescription>
              </CardHeader>

              <CardContent>
                <div className="space-y-3">
                  {/* Особенности */}
                  <div>
                    <Label className="text-sm font-medium">Возможности:</Label>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {integration.features.slice(0, 3).map((feature, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {feature}
                        </Badge>
                      ))}
                      {integration.features.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{integration.features.length - 3}
                        </Badge>
                      )}
                    </div>
                  </div>

                  {/* Сложность настройки */}
                  <div className="flex items-center justify-between">
                    <Label className="text-sm">Сложность:</Label>
                    <Badge variant={
                      integration.setupComplexity === 'easy' ? 'success' :
                      integration.setupComplexity === 'medium' ? 'secondary' : 'destructive'
                    }>
                      {integration.setupComplexity === 'easy' ? 'Легко' :
                       integration.setupComplexity === 'medium' ? 'Средне' : 'Сложно'}
                    </Badge>
                  </div>

                  {/* Цена */}
                  {integration.pricing && (
                    <div className="flex items-center justify-between">
                      <Label className="text-sm">Цена:</Label>
                      <span className="text-sm text-muted-foreground">{integration.pricing}</span>
                    </div>
                  )}

                  {/* Последняя синхронизация */}
                  {integration.lastSync && (
                    <div className="flex items-center justify-between">
                      <Label className="text-sm">Последняя синхронизация:</Label>
                      <span className="text-sm text-muted-foreground">{integration.lastSync}</span>
                    </div>
                  )}
                </div>
              </CardContent>

              <CardContent className="pt-0">
                <div className="flex gap-2">
                  {integration.status === 'connected' ? (
                    <>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setSelectedIntegration(integration)
                          setShowConfigDialog(true)
                        }}
                      >
                        <Settings className="h-4 w-4 mr-2" />
                        Настроить
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDisconnect(integration)}
                      >
                        Отключить
                      </Button>
                    </>
                  ) : integration.status === 'error' ? (
                    <>
                      <Button
                        size="sm"
                        onClick={() => {
                          setSelectedIntegration(integration)
                          setShowConfigDialog(true)
                        }}
                      >
                        <RefreshCw className="h-4 w-4 mr-2" />
                        Переподключить
                      </Button>
                      <Button variant="outline" size="sm">
                        <AlertCircle className="h-4 w-4 mr-2" />
                        Диагностика
                      </Button>
                    </>
                  ) : (
                    <Button
                      className="w-full"
                      onClick={() => {
                        setSelectedIntegration(integration)
                        setShowConfigDialog(true)
                      }}
                      disabled={isConnecting}
                    >
                      {isConnecting && selectedIntegration?.id === integration.id ? (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <Zap className="h-4 w-4 mr-2" />
                      )}
                      Подключить
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Диалог настройки интеграции */}
      <IntegrationConfigDialog
        integration={selectedIntegration}
        isOpen={showConfigDialog}
        onClose={() => setShowConfigDialog(false)}
        onConnect={handleConnect}
        isConnecting={isConnecting}
      />

      {/* Диалог добавления новой интеграции */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Plus className="h-5 w-5" />
              Добавить новую интеграцию
            </DialogTitle>
            <DialogDescription>
              Создайте пользовательскую интеграцию для подключения к вашему API или сервису
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6 py-4">
            {/* Основная информация */}
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="integration-name">Название интеграции *</Label>
                  <Input
                    id="integration-name"
                    placeholder="Например: My Custom API"
                    value={newIntegration.name}
                    onChange={(e) => setNewIntegration(prev => ({ ...prev, name: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="integration-category">Категория</Label>
                  <Select
                    value={newIntegration.category}
                    onValueChange={(value) => setNewIntegration(prev => ({ ...prev, category: value as Integration['category'] }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="payment">Платежи</SelectItem>
                      <SelectItem value="communication">Коммуникации</SelectItem>
                      <SelectItem value="analytics">Аналитика</SelectItem>
                      <SelectItem value="crm">CRM</SelectItem>
                      <SelectItem value="ecommerce">E-commerce</SelectItem>
                      <SelectItem value="marketing">Маркетинг</SelectItem>
                      <SelectItem value="support">Поддержка</SelectItem>
                      <SelectItem value="logistics">Логистика</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="integration-description">Описание *</Label>
                <Textarea
                  id="integration-description"
                  placeholder="Опишите, что делает эта интеграция..."
                  value={newIntegration.description}
                  onChange={(e) => setNewIntegration(prev => ({ ...prev, description: e.target.value }))}
                  rows={3}
                />
              </div>
            </div>

            <Separator />

            {/* Настройки API */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Key className="h-4 w-4" />
                <Label className="text-base font-medium">Настройки API</Label>
              </div>

              <div>
                <Label htmlFor="api-url">API URL</Label>
                <Input
                  id="api-url"
                  placeholder="https://api.example.com/v1"
                  value={newIntegration.apiUrl}
                  onChange={(e) => setNewIntegration(prev => ({ ...prev, apiUrl: e.target.value }))}
                />
              </div>

              <div>
                <Label htmlFor="api-key">API ключ</Label>
                <div className="flex gap-2">
                  <Input
                    id="api-key"
                    type={showApiKey ? "text" : "password"}
                    placeholder="Введите API ключ..."
                    value={newIntegration.apiKey}
                    onChange={(e) => setNewIntegration(prev => ({ ...prev, apiKey: e.target.value }))}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={() => setShowApiKey(!showApiKey)}
                  >
                    {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
              </div>

              <div>
                <Label htmlFor="webhook-url">Webhook URL (опционально)</Label>
                <Input
                  id="webhook-url"
                  placeholder="https://your-domain.com/webhooks/custom"
                  value={newIntegration.webhookUrl}
                  onChange={(e) => setNewIntegration(prev => ({ ...prev, webhookUrl: e.target.value }))}
                />
              </div>
            </div>

            <Separator />

            {/* Информационное сообщение */}
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                После создания интеграции вы сможете настроить дополнительные параметры в разделе конфигурации.
                Убедитесь, что ваш API поддерживает необходимые методы аутентификации.
              </AlertDescription>
            </Alert>
          </div>

          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setShowAddDialog(false)}>
              Отмена
            </Button>
            <Button onClick={handleAddIntegration}>
              <Plus className="h-4 w-4 mr-2" />
              Добавить интеграцию
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
