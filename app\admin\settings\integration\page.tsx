"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import {
  <PERSON>tings,
  Check,
  X,
  AlertCircle,
  Loader2,
  ExternalLink,
  Copy,
  Eye,
  EyeOff,
  RefreshCw,
  Zap,
  Shield,
  Globe,
  CreditCard,
  MessageSquare,
  Phone,
  Mail,
  BarChart3,
  Package,
  Truck,
  Users,
  Calendar,
  FileText,
  Webhook,
  Key,
  Database,
  Cloud,
  Lock,
  Plus,
  Wallet
} from "lucide-react"
import { toast } from "@/hooks/use-toast"
import { IntegrationConfigDialog } from "./integration-config-dialog"

// Типы интеграций
interface Integration {
  id: string
  name: string
  description: string
  category: 'payment' | 'communication' | 'analytics' | 'crm' | 'ecommerce' | 'marketing' | 'support' | 'logistics'
  icon: React.ElementType
  status: 'connected' | 'disconnected' | 'error' | 'configuring'
  isPopular?: boolean
  isPremium?: boolean
  setupComplexity: 'easy' | 'medium' | 'advanced'
  features: string[]
  pricing?: string
  documentation?: string
  supportedRegions?: string[]
  lastSync?: string
  config?: Record<string, any>
}

// Категории интеграций
const categories = [
  { id: 'all', name: 'Все интеграции', icon: Globe },
  { id: 'payment', name: 'Платежи', icon: CreditCard },
  { id: 'communication', name: 'Коммуникации', icon: MessageSquare },
  { id: 'analytics', name: 'Аналитика', icon: BarChart3 },
  { id: 'crm', name: 'CRM', icon: Users },
  { id: 'ecommerce', name: 'E-commerce', icon: Package },
  { id: 'marketing', name: 'Маркетинг', icon: Mail },
  { id: 'support', name: 'Поддержка', icon: Phone },
  { id: 'logistics', name: 'Логистика', icon: Truck }
]

// Доступные интеграции
const availableIntegrations: Integration[] = [
  {
    id: 'stripe',
    name: 'Stripe',
    description: 'Принимайте онлайн-платежи от клиентов по всему миру',
    category: 'payment',
    icon: CreditCard,
    status: 'disconnected',
    isPopular: true,
    setupComplexity: 'easy',
    features: ['Онлайн-платежи', 'Подписки', 'Мультивалютность', 'Fraud protection'],
    pricing: 'От 2.9% за транзакцию',
    documentation: 'https://stripe.com/docs',
    supportedRegions: ['US', 'EU', 'CA', 'AU']
  },
  {
    id: 'paypal',
    name: 'PayPal',
    description: 'Популярная платежная система для онлайн-торговли',
    category: 'payment',
    icon: Wallet,
    status: 'disconnected',
    isPopular: true,
    setupComplexity: 'easy',
    features: ['PayPal платежи', 'PayPal Credit', 'Международные платежи'],
    pricing: 'От 2.9% за транзакцию'
  },
  {
    id: 'signalwire',
    name: 'SignalWire',
    description: 'Облачная платформа для голосовых и видео коммуникаций',
    category: 'communication',
    icon: Phone,
    status: 'connected',
    setupComplexity: 'medium',
    features: ['Voice API', 'Video API', 'Messaging', 'AI интеграция'],
    pricing: 'От $0.0085 за минуту',
    lastSync: '2 минуты назад'
  },
  {
    id: 'twilio',
    name: 'Twilio',
    description: 'Платформа для SMS, голосовых вызовов и видеосвязи',
    category: 'communication',
    icon: MessageSquare,
    status: 'disconnected',
    isPopular: true,
    setupComplexity: 'medium',
    features: ['SMS API', 'Voice API', 'WhatsApp Business', 'Verify API'],
    pricing: 'От $0.0075 за SMS'
  },
  {
    id: 'google-analytics',
    name: 'Google Analytics',
    description: 'Веб-аналитика для отслеживания поведения пользователей',
    category: 'analytics',
    icon: BarChart3,
    status: 'connected',
    isPopular: true,
    setupComplexity: 'easy',
    features: ['Веб-аналитика', 'E-commerce tracking', 'Конверсии', 'Аудитории'],
    pricing: 'Бесплатно',
    lastSync: '1 час назад'
  },
  {
    id: 'hubspot',
    name: 'HubSpot',
    description: 'CRM и маркетинговая автоматизация',
    category: 'crm',
    icon: Users,
    status: 'disconnected',
    setupComplexity: 'medium',
    features: ['CRM', 'Email маркетинг', 'Lead scoring', 'Автоматизация'],
    pricing: 'От $45/месяц'
  },
  {
    id: 'shopify',
    name: 'Shopify',
    description: 'Платформа для создания интернет-магазинов',
    category: 'ecommerce',
    icon: Package,
    status: 'disconnected',
    isPopular: true,
    setupComplexity: 'easy',
    features: ['Управление товарами', 'Заказы', 'Клиенты', 'Отчеты'],
    pricing: 'От $29/месяц'
  },
  {
    id: 'mailchimp',
    name: 'Mailchimp',
    description: 'Email маркетинг и автоматизация',
    category: 'marketing',
    icon: Mail,
    status: 'disconnected',
    setupComplexity: 'easy',
    features: ['Email кампании', 'Автоматизация', 'A/B тестирование', 'Аналитика'],
    pricing: 'От $10/месяц'
  },
  {
    id: 'zendesk',
    name: 'Zendesk',
    description: 'Платформа для службы поддержки клиентов',
    category: 'support',
    icon: Phone,
    status: 'error',
    setupComplexity: 'medium',
    features: ['Тикет-система', 'Live chat', 'Knowledge base', 'Аналитика'],
    pricing: 'От $19/месяц'
  },
  {
    id: 'fedex',
    name: 'FedEx',
    description: 'Служба доставки и логистики',
    category: 'logistics',
    icon: Truck,
    status: 'disconnected',
    setupComplexity: 'advanced',
    features: ['Отслеживание посылок', 'Расчет стоимости', 'Печать этикеток'],
    pricing: 'По тарифам FedEx'
  }
]

export default function ImprovedIntegrationPage() {
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [integrations, setIntegrations] = useState<Integration[]>(availableIntegrations)
  const [selectedIntegration, setSelectedIntegration] = useState<Integration | null>(null)
  const [showConfigDialog, setShowConfigDialog] = useState(false)
  const [isConnecting, setIsConnecting] = useState(false)
  const [showApiKey, setShowApiKey] = useState(false)

  // Фильтрация интеграций
  const filteredIntegrations = integrations.filter(integration => {
    const matchesCategory = selectedCategory === 'all' || integration.category === selectedCategory
    const matchesSearch = integration.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         integration.description.toLowerCase().includes(searchQuery.toLowerCase())
    return matchesCategory && matchesSearch
  })

  // Статистика
  const stats = {
    total: integrations.length,
    connected: integrations.filter(i => i.status === 'connected').length,
    errors: integrations.filter(i => i.status === 'error').length
  }

  // Получение иконки статуса
  const getStatusIcon = (status: Integration['status']) => {
    switch (status) {
      case 'connected': return <Check className="h-4 w-4 text-green-500" />
      case 'error': return <X className="h-4 w-4 text-red-500" />
      case 'configuring': return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />
      default: return <AlertCircle className="h-4 w-4 text-gray-400" />
    }
  }

  // Получение цвета бейджа статуса
  const getStatusBadgeVariant = (status: Integration['status']) => {
    switch (status) {
      case 'connected': return 'success' as const
      case 'error': return 'destructive' as const
      case 'configuring': return 'secondary' as const
      default: return 'outline' as const
    }
  }

  // Получение текста статуса
  const getStatusText = (status: Integration['status']) => {
    switch (status) {
      case 'connected': return 'Подключено'
      case 'error': return 'Ошибка'
      case 'configuring': return 'Настройка'
      default: return 'Не подключено'
    }
  }

  // Подключение интеграции
  const handleConnect = async (integration: Integration) => {
    setIsConnecting(true)

    // Симуляция подключения
    await new Promise(resolve => setTimeout(resolve, 2000))

    setIntegrations(prev => prev.map(i =>
      i.id === integration.id
        ? {
            ...i,
            status: 'connected' as const,
            lastSync: 'Только что',
            config: integration.config // Сохраняем конфигурацию
          }
        : i
    ))

    setIsConnecting(false)
    setShowConfigDialog(false)

    toast({
      title: "Интеграция подключена",
      description: `${integration.name} успешно подключен к вашей системе.`
    })
  }

  // Отключение интеграции
  const handleDisconnect = async (integration: Integration) => {
    setIntegrations(prev => prev.map(i =>
      i.id === integration.id
        ? { ...i, status: 'disconnected' as const, lastSync: undefined }
        : i
    ))

    toast({
      title: "Интеграция отключена",
      description: `${integration.name} отключен от вашей системы.`
    })
  }

  return (
    <div className="space-y-6">
      {/* Заголовок */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Интеграции</h1>
          <p className="text-muted-foreground mt-2">
            Управляйте интеграциями с внешними сервисами и системами
          </p>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Добавить интеграцию
        </Button>
      </div>

      {/* Статистика */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Всего интеграций</CardTitle>
            <Globe className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Подключено</CardTitle>
            <Check className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.connected}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ошибки</CardTitle>
            <AlertCircle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.errors}</div>
          </CardContent>
        </Card>
      </div>

      {/* Фильтры и поиск */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <Input
            placeholder="Поиск интеграций..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="max-w-sm"
          />
        </div>
        <Tabs value={selectedCategory} onValueChange={setSelectedCategory} className="w-full sm:w-auto">
          <TabsList className="grid w-full grid-cols-3 sm:grid-cols-9">
            {categories.map((category) => {
              const Icon = category.icon
              return (
                <TabsTrigger key={category.id} value={category.id} className="flex items-center gap-1">
                  <Icon className="h-4 w-4" />
                  <span className="hidden sm:inline">{category.name}</span>
                </TabsTrigger>
              )
            })}
          </TabsList>
        </Tabs>
      </div>

      {/* Список интеграций */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {filteredIntegrations.map((integration) => {
          const Icon = integration.icon
          return (
            <Card key={integration.id} className="relative">
              {integration.isPopular && (
                <Badge className="absolute -top-2 -right-2 bg-orange-500">
                  Популярное
                </Badge>
              )}
              {integration.isPremium && (
                <Badge className="absolute -top-2 -right-2 bg-purple-500">
                  Premium
                </Badge>
              )}

              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-muted rounded-lg">
                      <Icon className="h-6 w-6" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{integration.name}</CardTitle>
                      <div className="flex items-center gap-2 mt-1">
                        {getStatusIcon(integration.status)}
                        <Badge variant={getStatusBadgeVariant(integration.status)}>
                          {getStatusText(integration.status)}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
                <CardDescription className="mt-2">
                  {integration.description}
                </CardDescription>
              </CardHeader>

              <CardContent>
                <div className="space-y-3">
                  {/* Особенности */}
                  <div>
                    <Label className="text-sm font-medium">Возможности:</Label>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {integration.features.slice(0, 3).map((feature, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {feature}
                        </Badge>
                      ))}
                      {integration.features.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{integration.features.length - 3}
                        </Badge>
                      )}
                    </div>
                  </div>

                  {/* Сложность настройки */}
                  <div className="flex items-center justify-between">
                    <Label className="text-sm">Сложность:</Label>
                    <Badge variant={
                      integration.setupComplexity === 'easy' ? 'success' :
                      integration.setupComplexity === 'medium' ? 'secondary' : 'destructive'
                    }>
                      {integration.setupComplexity === 'easy' ? 'Легко' :
                       integration.setupComplexity === 'medium' ? 'Средне' : 'Сложно'}
                    </Badge>
                  </div>

                  {/* Цена */}
                  {integration.pricing && (
                    <div className="flex items-center justify-between">
                      <Label className="text-sm">Цена:</Label>
                      <span className="text-sm text-muted-foreground">{integration.pricing}</span>
                    </div>
                  )}

                  {/* Последняя синхронизация */}
                  {integration.lastSync && (
                    <div className="flex items-center justify-between">
                      <Label className="text-sm">Последняя синхронизация:</Label>
                      <span className="text-sm text-muted-foreground">{integration.lastSync}</span>
                    </div>
                  )}
                </div>
              </CardContent>

              <CardContent className="pt-0">
                <div className="flex gap-2">
                  {integration.status === 'connected' ? (
                    <>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setSelectedIntegration(integration)
                          setShowConfigDialog(true)
                        }}
                      >
                        <Settings className="h-4 w-4 mr-2" />
                        Настроить
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDisconnect(integration)}
                      >
                        Отключить
                      </Button>
                    </>
                  ) : integration.status === 'error' ? (
                    <>
                      <Button
                        size="sm"
                        onClick={() => {
                          setSelectedIntegration(integration)
                          setShowConfigDialog(true)
                        }}
                      >
                        <RefreshCw className="h-4 w-4 mr-2" />
                        Переподключить
                      </Button>
                      <Button variant="outline" size="sm">
                        <AlertCircle className="h-4 w-4 mr-2" />
                        Диагностика
                      </Button>
                    </>
                  ) : (
                    <Button
                      className="w-full"
                      onClick={() => {
                        setSelectedIntegration(integration)
                        setShowConfigDialog(true)
                      }}
                      disabled={isConnecting}
                    >
                      {isConnecting && selectedIntegration?.id === integration.id ? (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <Zap className="h-4 w-4 mr-2" />
                      )}
                      Подключить
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Диалог настройки интеграции */}
      <IntegrationConfigDialog
        integration={selectedIntegration}
        isOpen={showConfigDialog}
        onClose={() => setShowConfigDialog(false)}
        onConnect={handleConnect}
        isConnecting={isConnecting}
      />
    </div>
  )
}
