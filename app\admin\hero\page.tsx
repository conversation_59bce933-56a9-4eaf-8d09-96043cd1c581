"use client"

import type React from "react"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"

export default function AdminHeroPage() {
  const { toast } = useToast()
  const [heroData, setHeroData] = useState({
    title: "Transform Your Business with Our Platform",
    subtitle: "Streamline operations, boost productivity, and drive growth with our all-in-one solution.",
    primaryButton: "Get Started",
    secondaryButton: "Learn More",
    imageUrl: "/placeholder.svg?height=550&width=550&query=modern%20dashboard%20interface",
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // В реальном приложении здесь был бы API запрос
    toast({
      title: "Успешно сохранено",
      description: "Hero секция обновлена",
    })
  }

  return (
    <div>
      <h1 className="text-3xl font-bold mb-8">Редактировать Hero секцию</h1>

      <div className="grid gap-6 lg:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Контент</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <Label htmlFor="title">Заголовок</Label>
                <Input
                  id="title"
                  value={heroData.title}
                  onChange={(e) => setHeroData({ ...heroData, title: e.target.value })}
                />
              </div>

              <div>
                <Label htmlFor="subtitle">Подзаголовок</Label>
                <Textarea
                  id="subtitle"
                  value={heroData.subtitle}
                  onChange={(e) => setHeroData({ ...heroData, subtitle: e.target.value })}
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="primaryButton">Текст главной кнопки</Label>
                <Input
                  id="primaryButton"
                  value={heroData.primaryButton}
                  onChange={(e) => setHeroData({ ...heroData, primaryButton: e.target.value })}
                />
              </div>

              <div>
                <Label htmlFor="secondaryButton">Текст второй кнопки</Label>
                <Input
                  id="secondaryButton"
                  value={heroData.secondaryButton}
                  onChange={(e) => setHeroData({ ...heroData, secondaryButton: e.target.value })}
                />
              </div>

              <div>
                <Label htmlFor="imageUrl">URL изображения</Label>
                <Input
                  id="imageUrl"
                  value={heroData.imageUrl}
                  onChange={(e) => setHeroData({ ...heroData, imageUrl: e.target.value })}
                />
              </div>

              <Button type="submit" className="w-full">
                Сохранить изменения
              </Button>
            </form>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Предпросмотр</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="border rounded-lg p-6 bg-gray-50">
              <h2 className="text-2xl font-bold mb-2">{heroData.title}</h2>
              <p className="text-gray-600 mb-4">{heroData.subtitle}</p>
              <div className="flex gap-2">
                <Button>{heroData.primaryButton}</Button>
                <Button variant="outline">{heroData.secondaryButton}</Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
