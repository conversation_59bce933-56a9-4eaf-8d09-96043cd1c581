import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"

export function TestimonialsSection() {
  return (
    <section id="testimonials" className="w-full py-12 md:py-24 lg:py-32">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <div className="inline-block rounded-lg bg-primary px-3 py-1 text-sm text-primary-foreground">
              Testimonials
            </div>
            <h2 className="text-3xl font-bold tracking-tighter md:text-4xl/tight">
              Trusted by Thousands of Businesses
            </h2>
            <p className="max-w-[900px] text-muted-foreground md:text-xl/relaxed">
              Don&apos;t just take our word for it. Here&apos;s what our customers have to say.
            </p>
          </div>
        </div>
        <div className="mx-auto grid max-w-5xl gap-6 py-12 lg:grid-cols-3">
          <Card>
            <CardContent className="p-6">
              <div className="flex flex-col items-start gap-4">
                <div className="flex items-center gap-4">
                  <Image
                    src="/placeholder.svg?height=40&width=40&query=professional%20woman%20portrait"
                    width={40}
                    height={40}
                    alt="Sarah Johnson"
                    className="rounded-full"
                  />
                  <div>
                    <h3 className="font-semibold">Sarah Johnson</h3>
                    <p className="text-sm text-muted-foreground">Marketing Director</p>
                  </div>
                </div>
                <div className="flex text-primary">
                  {[...Array(5)].map((_, i) => (
                    <svg
                      key={i}
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="currentColor"
                      className="h-5 w-5"
                    >
                      <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                    </svg>
                  ))}
                </div>
                <p className="text-muted-foreground">
                  &ldquo;This platform has transformed how our marketing team operates. The analytics tools are powerful yet
                  intuitive, and the automation features have saved us countless hours.&rdquo;
                </p>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex flex-col items-start gap-4">
                <div className="flex items-center gap-4">
                  <Image
                    src="/placeholder.svg?height=40&width=40&query=professional%20man%20portrait"
                    width={40}
                    height={40}
                    alt="David Chen"
                    className="rounded-full"
                  />
                  <div>
                    <h3 className="font-semibold">David Chen</h3>
                    <p className="text-sm text-muted-foreground">CTO</p>
                  </div>
                </div>
                <div className="flex text-primary">
                  {[...Array(5)].map((_, i) => (
                    <svg
                      key={i}
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="currentColor"
                      className="h-5 w-5"
                    >
                      <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                    </svg>
                  ))}
                </div>
                <p className="text-muted-foreground">
                  &ldquo;The security features are top-notch, and the scalability has allowed us to grow without worrying
                  about our tech stack. Integration with our existing tools was seamless.&rdquo;
                </p>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex flex-col items-start gap-4">
                <div className="flex items-center gap-4">
                  <Image
                    src="/placeholder.svg?height=40&width=40&query=professional%20woman%20portrait%20glasses"
                    width={40}
                    height={40}
                    alt="Emily Rodriguez"
                    className="rounded-full"
                  />
                  <div>
                    <h3 className="font-semibold">Emily Rodriguez</h3>
                    <p className="text-sm text-muted-foreground">Small Business Owner</p>
                  </div>
                </div>
                <div className="flex text-primary">
                  {[...Array(5)].map((_, i) => (
                    <svg
                      key={i}
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="currentColor"
                      className="h-5 w-5"
                    >
                      <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                    </svg>
                  ))}
                </div>
                <p className="text-muted-foreground">
                  &ldquo;As a small business, we needed a solution that was both affordable and powerful. This platform
                  delivers on both fronts, and the customer support has been exceptional.&rdquo;
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  )
}
