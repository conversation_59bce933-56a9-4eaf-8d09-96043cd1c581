"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Trash2, Plus } from "lucide-react"

interface Feature {
  id: number
  title: string
  description: string
  icon: string
}

interface Testimonial {
  id: number
  name: string
  position: string
  company: string
  content: string
  rating: number
  avatar: string
}

interface PricingPlan {
  id: string
  name: string
  price: number
  description: string
  features: string[]
  isPopular: boolean
}

interface FooterLink {
  id: string
  text: string
  url: string
}

interface FooterSection {
  id: string
  title: string
  links: FooterLink[]
}

interface FooterSocialLink {
  id: string
  platform: string
  icon: string
  url: string
}

interface FooterStyles {
  background: string
  textColor: string
  linkColor: string
  linkHoverColor: string
  borderColor: string
}

interface Footer {
  id: number
  content: string
}

export default function LandingSettingsPage() {
  const { toast } = useToast()

  // Hero section state
  const [heroData, setHeroData] = useState({
    title: "Transform Your Business with Our Platform",
    subtitle: "Streamline operations, boost productivity, and drive growth with our all-in-one solution.",
    primaryButton: "Get Started",
    secondaryButton: "Learn More",
    imageUrl: "/placeholder.svg?height=550&width=550&query=modern%20dashboard%20interface",
  })

  // Features state
  const [features, setFeatures] = useState<Feature[]>([
    {
      id: 1,
      title: "Advanced Analytics",
      description: "Gain valuable insights with our powerful analytics tools.",
      icon: "BarChart3",
    },
    {
      id: 2,
      title: "Time Saving",
      description: "Automate repetitive tasks and streamline your workflow.",
      icon: "Clock",
    },
    {
      id: 3,
      title: "Seamless Integration",
      description: "Connect with your favorite tools and services.",
      icon: "RefreshCw",
    },
  ])

  const [newFeature, setNewFeature] = useState({
    title: "",
    description: "",
    icon: "",
  })

  // Testimonials state
  const [testimonials, setTestimonials] = useState<Testimonial[]>([
    {
      id: 1,
      name: "Sarah Johnson",
      position: "Marketing Director",
      company: "Tech Corp",
      content: "This platform has transformed how our marketing team operates.",
      rating: 5,
      avatar: "/placeholder.svg?height=40&width=40&query=professional%20woman%20portrait",
    },
    {
      id: 2,
      name: "David Chen",
      position: "CTO",
      company: "StartupXYZ",
      content: "The security features are top-notch, and the scalability has allowed us to grow.",
      rating: 5,
      avatar: "/placeholder.svg?height=40&width=40&query=professional%20man%20portrait",
    },
  ])

  const [newTestimonial, setNewTestimonial] = useState({
    name: "",
    position: "",
    company: "",
    content: "",
    rating: 5,
    avatar: "",
  })

  // Pricing state
  const [plans, setPlans] = useState<PricingPlan[]>([
    {
      id: "starter",
      name: "Starter",
      price: 29,
      description: "Perfect for small teams just getting started",
      features: ["Up to 5 users", "Basic analytics", "24-hour support", "1GB storage"],
      isPopular: false,
    },
    {
      id: "professional",
      name: "Professional",
      price: 79,
      description: "Ideal for growing businesses",
      features: ["Up to 20 users", "Advanced analytics", "Priority support", "10GB storage", "Custom integrations"],
      isPopular: true,
    },
    {
      id: "enterprise",
      name: "Enterprise",
      price: 199,
      description: "For large organizations with specific needs",
      features: [
        "Unlimited users",
        "Enterprise analytics",
        "24/7 dedicated support",
        "Unlimited storage",
        "Custom integrations",
        "On-premise option",
        "SLA guarantee",
      ],
      isPopular: false,
    },
  ])

  // Footer state
  const [footerSections, setFooterSections] = useState<FooterSection[]>([
    {
      id: "company",
      title: "Company",
      links: [
        { id: "about", text: "About", url: "#" },
        { id: "blog", text: "Blog", url: "#" },
        { id: "careers", text: "Careers", url: "#" },
        { id: "contact", text: "Contact", url: "#" },
      ],
    },
    {
      id: "product",
      title: "Product",
      links: [
        { id: "features", text: "Features", url: "#" },
        { id: "pricing", text: "Pricing", url: "#" },
        { id: "integrations", text: "Integrations", url: "#" },
        { id: "changelog", text: "Changelog", url: "#" },
      ],
    },
    {
      id: "legal",
      title: "Legal",
      links: [
        { id: "terms", text: "Terms", url: "#" },
        { id: "privacy", text: "Privacy", url: "#" },
        { id: "cookies", text: "Cookies", url: "#" },
        { id: "licenses", text: "Licenses", url: "#" },
      ],
    },
  ])

  const [footerSocialLinks, setFooterSocialLinks] = useState<FooterSocialLink[]>([
    { id: "facebook", platform: "Facebook", icon: "Facebook", url: "https://facebook.com" },
    { id: "twitter", platform: "Twitter", icon: "Twitter", url: "https://twitter.com" },
    { id: "instagram", platform: "Instagram", icon: "Instagram", url: "https://instagram.com" },
    { id: "linkedin", platform: "LinkedIn", icon: "Linkedin", url: "https://linkedin.com" },
  ])

  const [footerCopyright, setFooterCopyright] = useState("© 2023 ACME Inc. All rights reserved.")

  const [footerStyles, setFooterStyles] = useState<FooterStyles>({
    background: "#f9fafb",
    textColor: "#6b7280",
    linkColor: "#6b7280",
    linkHoverColor: "#111827",
    borderColor: "#e5e7eb",
  })

  const [footerLogo, setFooterLogo] = useState({
    enabled: true,
    url: "/logo.svg",
  })

  // General settings state
  const [generalSettings, setGeneralSettings] = useState({
    title: "ACME - Инновационные решения для бизнеса",
    description: "Streamline operations, boost productivity, and drive growth with our all-in-one solution.",
    keywords: "business, productivity, growth, solution",
    favicon: "/favicon.ico",
    language: "ru",
  })

  const [analyticsSettings, setAnalyticsSettings] = useState({
    googleAnalyticsId: "",
    enableGoogleAnalytics: false,
    facebookPixelId: "",
    enableFacebookPixel: false,
    yandexMetrikaId: "",
    enableYandexMetrika: true,
  })

  const [socialSettings, setGeneralSocialSettings] = useState({
    ogTitle: "ACME - Инновационные решения для бизнеса",
    ogDescription: "Streamline operations, boost productivity, and drive growth with our all-in-one solution.",
    ogImage: "/og-image.jpg",
    twitterHandle: "@acmecompany",
  })

  const [advancedSettings, setAdvancedSettings] = useState({
    customCss: "",
    customJs: "",
    robotsTxt: "User-agent: *\nAllow: /",
    sitemapEnabled: true,
  })

  // Hero section handlers
  const handleHeroSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    toast({
      title: "Успешно сохранено",
      description: "Hero секция обновлена",
    })
  }

  // Features handlers
  const addFeature = () => {
    if (newFeature.title && newFeature.description) {
      setFeatures([
        ...features,
        {
          id: Date.now(),
          ...newFeature,
        },
      ])
      setNewFeature({ title: "", description: "", icon: "" })
      toast({
        title: "Функция добавлена",
        description: "Новая функция успешно добавлена",
      })
    }
  }

  const deleteFeature = (id: number) => {
    setFeatures(features.filter((f) => f.id !== id))
    toast({
      title: "Функция удалена",
      description: "Функция успешно удалена",
    })
  }

  // Testimonials handlers
  const addTestimonial = () => {
    if (newTestimonial.name && newTestimonial.content) {
      setTestimonials([
        ...testimonials,
        {
          id: Date.now(),
          ...newTestimonial,
        },
      ])
      setNewTestimonial({
        name: "",
        position: "",
        company: "",
        content: "",
        rating: 5,
        avatar: "",
      })
      toast({
        title: "Отзыв добавлен",
        description: "Новый отзыв успешно добавлен",
      })
    }
  }

  const deleteTestimonial = (id: number) => {
    setTestimonials(testimonials.filter((t) => t.id !== id))
    toast({
      title: "Отзыв удален",
      description: "Отзыв успешно удален",
    })
  }

  // Pricing handlers
  const updatePlan = (id: string, field: keyof PricingPlan, value: any) => {
    setPlans(plans.map((plan) => (plan.id === id ? { ...plan, [field]: value } : plan)))
  }

  const updateFeature = (planId: string, index: number, value: string) => {
    setPlans(
      plans.map((plan) => {
        if (plan.id === planId) {
          const newFeatures = [...plan.features]
          newFeatures[index] = value
          return { ...plan, features: newFeatures }
        }
        return plan
      }),
    )
  }

  const addPlanFeature = (planId: string) => {
    setPlans(
      plans.map((plan) => {
        if (plan.id === planId) {
          return { ...plan, features: [...plan.features, ""] }
        }
        return plan
      }),
    )
  }

  const removePlanFeature = (planId: string, index: number) => {
    setPlans(
      plans.map((plan) => {
        if (plan.id === planId) {
          return { ...plan, features: plan.features.filter((_, i) => i !== index) }
        }
        return plan
      }),
    )
  }

  // General settings handlers
  const updateGeneralSetting = (key: keyof typeof generalSettings, value: string) => {
    setGeneralSettings({
      ...generalSettings,
      [key]: value,
    })
  }

  const updateAnalyticsSetting = (key: keyof typeof analyticsSettings, value: string | boolean) => {
    setAnalyticsSettings({
      ...analyticsSettings,
      [key]: value,
    })
  }

  const updateSocialSetting = (key: keyof typeof socialSettings, value: string) => {
    setGeneralSocialSettings({
      ...socialSettings,
      [key]: value,
    })
  }

  const updateAdvancedSetting = (key: keyof typeof advancedSettings, value: string | boolean) => {
    setAdvancedSettings({
      ...advancedSettings,
      [key]: value,
    })
  }

  // Функции для управления секциями и ссылками
  const updateSectionTitle = (sectionId: string, newTitle: string) => {
    setFooterSections(
      footerSections.map((section) => (section.id === sectionId ? { ...section, title: newTitle } : section)),
    )
  }

  const addSection = () => {
    const newId = `section-${Date.now()}`
    setFooterSections([
      ...footerSections,
      {
        id: newId,
        title: "New Section",
        links: [],
      },
    ])
  }

  const removeSection = (sectionId: string) => {
    setFooterSections(footerSections.filter((section) => section.id !== sectionId))
  }

  const addLink = (sectionId: string) => {
    setFooterSections(
      footerSections.map((section) => {
        if (section.id === sectionId) {
          const newId = `link-${Date.now()}`
          return {
            ...section,
            links: [...section.links, { id: newId, text: "New Link", url: "#" }],
          }
        }
        return section
      }),
    )
  }

  const updateLink = (sectionId: string, linkId: string, field: keyof FooterLink, value: string) => {
    setFooterSections(
      footerSections.map((section) => {
        if (section.id === sectionId) {
          return {
            ...section,
            links: section.links.map((link) => (link.id === linkId ? { ...link, [field]: value } : link)),
          }
        }
        return section
      }),
    )
  }

  const removeLink = (sectionId: string, linkId: string) => {
    setFooterSections(
      footerSections.map((section) => {
        if (section.id === sectionId) {
          return {
            ...section,
            links: section.links.filter((link) => link.id !== linkId),
          }
        }
        return section
      }),
    )
  }

  // Функции для управления социальными сетями
  const addSocialLink = () => {
    const newId = `social-${Date.now()}`
    setFooterSocialLinks([...footerSocialLinks, { id: newId, platform: "New Platform", icon: "Globe", url: "#" }])
  }

  const updateSocialLink = (id: string, field: keyof FooterSocialLink, value: string) => {
    setFooterSocialLinks(footerSocialLinks.map((link) => (link.id === id ? { ...link, [field]: value } : link)))
  }

  const removeSocialLink = (id: string) => {
    setFooterSocialLinks(footerSocialLinks.filter((link) => link.id !== id))
  }

  // Функция для обновления стилей
  const updateFooterStyle = (field: keyof FooterStyles, value: string) => {
    setFooterStyles({
      ...footerStyles,
      [field]: value,
    })
  }

  // Заменяем старую функцию updateFooter
  const updateFooterCopyright = (value: string) => {
    setFooterCopyright(value)
  }

  const toggleFooterLogo = (enabled: boolean) => {
    setFooterLogo({
      ...footerLogo,
      enabled,
    })
  }

  const updateFooterLogo = (url: string) => {
    setFooterLogo({
      ...footerLogo,
      url,
    })
  }

  const saveChanges = () => {
    toast({
      title: "Настройки сохранены",
      description: "Все настройки лендинга успешно обновлены",
    })
  }

  return (
    <div>
      <h1 className="text-3xl font-bold mb-8">Настройки лендинга</h1>

      <Tabs defaultValue="hero" className="mb-8">
        <TabsList className="grid w-full grid-cols-4 lg:grid-cols-9">
          <TabsTrigger value="hero">Hero</TabsTrigger>
          <TabsTrigger value="features">Функции</TabsTrigger>
          <TabsTrigger value="testimonials">Отзывы</TabsTrigger>
          <TabsTrigger value="pricing">Цены</TabsTrigger>
          <TabsTrigger value="general">Общие</TabsTrigger>
          <TabsTrigger value="analytics">Аналитика</TabsTrigger>
          <TabsTrigger value="social">Соцсети</TabsTrigger>
          <TabsTrigger value="advanced">Расширенные</TabsTrigger>
          <TabsTrigger value="footer">Футер</TabsTrigger>
        </TabsList>

        <TabsContent value="hero">
          <div className="grid gap-6 lg:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Редактировать Hero секцию</CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleHeroSubmit} className="space-y-4">
                  <div>
                    <Label htmlFor="title">Заголовок</Label>
                    <Input
                      id="title"
                      value={heroData.title}
                      onChange={(e) => setHeroData({ ...heroData, title: e.target.value })}
                    />
                  </div>

                  <div>
                    <Label htmlFor="subtitle">Подзаголовок</Label>
                    <Textarea
                      id="subtitle"
                      value={heroData.subtitle}
                      onChange={(e) => setHeroData({ ...heroData, subtitle: e.target.value })}
                      rows={3}
                    />
                  </div>

                  <div>
                    <Label htmlFor="primaryButton">Текст главной кнопки</Label>
                    <Input
                      id="primaryButton"
                      value={heroData.primaryButton}
                      onChange={(e) => setHeroData({ ...heroData, primaryButton: e.target.value })}
                    />
                  </div>

                  <div>
                    <Label htmlFor="secondaryButton">Текст второй кнопки</Label>
                    <Input
                      id="secondaryButton"
                      value={heroData.secondaryButton}
                      onChange={(e) => setHeroData({ ...heroData, secondaryButton: e.target.value })}
                    />
                  </div>

                  <div>
                    <Label htmlFor="imageUrl">URL изображения</Label>
                    <Input
                      id="imageUrl"
                      value={heroData.imageUrl}
                      onChange={(e) => setHeroData({ ...heroData, imageUrl: e.target.value })}
                    />
                  </div>

                  <Button type="submit" className="w-full">
                    Сохранить изменения
                  </Button>
                </form>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Предпросмотр</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="border rounded-lg p-6 bg-gray-50">
                  <h2 className="text-2xl font-bold mb-2">{heroData.title}</h2>
                  <p className="text-gray-600 mb-4">{heroData.subtitle}</p>
                  <div className="flex gap-2">
                    <Button>{heroData.primaryButton}</Button>
                    <Button variant="outline">{heroData.secondaryButton}</Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="features">
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Добавить новую функцию</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <Label htmlFor="feature-title">Название</Label>
                  <Input
                    id="feature-title"
                    value={newFeature.title}
                    onChange={(e) => setNewFeature({ ...newFeature, title: e.target.value })}
                    placeholder="Название функции"
                  />
                </div>
                <div>
                  <Label htmlFor="feature-icon">Иконка (название из Lucide)</Label>
                  <Input
                    id="feature-icon"
                    value={newFeature.icon}
                    onChange={(e) => setNewFeature({ ...newFeature, icon: e.target.value })}
                    placeholder="Например: BarChart3"
                  />
                </div>
                <div className="md:col-span-2">
                  <Label htmlFor="feature-description">Описание</Label>
                  <Textarea
                    id="feature-description"
                    value={newFeature.description}
                    onChange={(e) => setNewFeature({ ...newFeature, description: e.target.value })}
                    placeholder="Описание функции"
                    rows={3}
                  />
                </div>
                <div className="md:col-span-2">
                  <Button onClick={addFeature} className="w-full">
                    <Plus className="mr-2 h-4 w-4" />
                    Добавить функцию
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid gap-4">
            <h2 className="text-xl font-semibold">Текущие функции</h2>
            {features.map((feature) => (
              <Card key={feature.id}>
                <CardContent className="flex items-center justify-between p-4">
                  <div className="flex-1">
                    <h3 className="font-semibold">{feature.title}</h3>
                    <p className="text-sm text-muted-foreground">{feature.description}</p>
                    <p className="text-xs text-muted-foreground mt-1">Иконка: {feature.icon}</p>
                  </div>
                  <Button variant="destructive" size="sm" onClick={() => deleteFeature(feature.id)}>
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="testimonials">
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Добавить новый отзыв</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <Label htmlFor="testimonial-name">Имя</Label>
                  <Input
                    id="testimonial-name"
                    value={newTestimonial.name}
                    onChange={(e) => setNewTestimonial({ ...newTestimonial, name: e.target.value })}
                    placeholder="Имя клиента"
                  />
                </div>
                <div>
                  <Label htmlFor="testimonial-position">Должность</Label>
                  <Input
                    id="testimonial-position"
                    value={newTestimonial.position}
                    onChange={(e) => setNewTestimonial({ ...newTestimonial, position: e.target.value })}
                    placeholder="Должность"
                  />
                </div>
                <div>
                  <Label htmlFor="testimonial-company">Компания</Label>
                  <Input
                    id="testimonial-company"
                    value={newTestimonial.company}
                    onChange={(e) => setNewTestimonial({ ...newTestimonial, company: e.target.value })}
                    placeholder="Название компании"
                  />
                </div>
                <div>
                  <Label htmlFor="testimonial-rating">Рейтинг</Label>
                  <Select
                    value={newTestimonial.rating.toString()}
                    onValueChange={(value) => setNewTestimonial({ ...newTestimonial, rating: Number.parseInt(value) })}
                  >
                    <SelectTrigger id="testimonial-rating">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="5">5 звезд</SelectItem>
                      <SelectItem value="4">4 звезды</SelectItem>
                      <SelectItem value="3">3 звезды</SelectItem>
                      <SelectItem value="2">2 звезды</SelectItem>
                      <SelectItem value="1">1 звезда</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="md:col-span-2">
                  <Label htmlFor="testimonial-content">Отзыв</Label>
                  <Textarea
                    id="testimonial-content"
                    value={newTestimonial.content}
                    onChange={(e) => setNewTestimonial({ ...newTestimonial, content: e.target.value })}
                    placeholder="Текст отзыва"
                    rows={3}
                  />
                </div>
                <div className="md:col-span-2">
                  <Label htmlFor="testimonial-avatar">URL аватара</Label>
                  <Input
                    id="testimonial-avatar"
                    value={newTestimonial.avatar}
                    onChange={(e) => setNewTestimonial({ ...newTestimonial, avatar: e.target.value })}
                    placeholder="URL изображения"
                  />
                </div>
                <div className="md:col-span-2">
                  <Button onClick={addTestimonial} className="w-full">
                    <Plus className="mr-2 h-4 w-4" />
                    Добавить отзыв
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid gap-4">
            <h2 className="text-xl font-semibold">Текущие отзывы</h2>
            {testimonials.map((testimonial) => (
              <Card key={testimonial.id}>
                <CardContent className="flex items-start justify-between p-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="font-semibold">{testimonial.name}</h3>
                      <div className="flex text-yellow-500">
                        {[...Array(testimonial.rating)].map((_, i) => (
                          <svg
                            key={i}
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="16"
                            viewBox="0 0 24 24"
                            fill="currentColor"
                          >
                            <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                          </svg>
                        ))}
                      </div>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {testimonial.position} в {testimonial.company}
                    </p>
                    <p className="mt-2">{testimonial.content}</p>
                  </div>
                  <Button variant="destructive" size="sm" onClick={() => deleteTestimonial(testimonial.id)}>
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="pricing">
          <div className="grid gap-6 md:grid-cols-3 mb-8">
            {plans.map((plan) => (
              <Card key={plan.id} className={plan.isPopular ? "border-primary" : ""}>
                <CardHeader>
                  <CardTitle>
                    <Input
                      value={plan.name}
                      onChange={(e) => updatePlan(plan.id, "name", e.target.value)}
                      className="font-bold text-lg"
                    />
                  </CardTitle>
                  <div className="flex items-center gap-2 mt-2">
                    <Switch
                      checked={plan.isPopular}
                      onCheckedChange={(checked) => updatePlan(plan.id, "isPopular", checked)}
                    />
                    <Label>Популярный тариф</Label>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label>Цена ($/месяц)</Label>
                    <Input
                      type="number"
                      value={plan.price}
                      onChange={(e) => updatePlan(plan.id, "price", Number.parseInt(e.target.value))}
                    />
                  </div>

                  <div>
                    <Label>Описание</Label>
                    <Input
                      value={plan.description}
                      onChange={(e) => updatePlan(plan.id, "description", e.target.value)}
                    />
                  </div>

                  <div>
                    <Label>Функции</Label>
                    <div className="space-y-2 mt-2">
                      {plan.features.map((feature, index) => (
                        <div key={index} className="flex gap-2">
                          <Input
                            value={feature}
                            onChange={(e) => updateFeature(plan.id, index, e.target.value)}
                            placeholder="Функция"
                          />
                          <Button variant="destructive" size="sm" onClick={() => removePlanFeature(plan.id, index)}>
                            ×
                          </Button>
                        </div>
                      ))}
                      <Button variant="outline" size="sm" onClick={() => addPlanFeature(plan.id)} className="w-full">
                        Добавить функцию
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="general">
          <Card>
            <CardHeader>
              <CardTitle>Общие настройки</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="general-title">Заголовок сайта (title)</Label>
                <Input
                  id="general-title"
                  value={generalSettings.title}
                  onChange={(e) => updateGeneralSetting("title", e.target.value)}
                />
                <p className="text-sm text-muted-foreground mt-1">
                  Отображается в заголовке вкладки браузера и в результатах поиска.
                </p>
              </div>

              <div>
                <Label htmlFor="general-description">Описание сайта (meta description)</Label>
                <Textarea
                  id="general-description"
                  value={generalSettings.description}
                  onChange={(e) => updateGeneralSetting("description", e.target.value)}
                  rows={3}
                />
                <p className="text-sm text-muted-foreground mt-1">
                  Краткое описание сайта для поисковых систем (до 160 символов).
                </p>
              </div>

              <div>
                <Label htmlFor="general-keywords">Ключевые слова (meta keywords)</Label>
                <Input
                  id="general-keywords"
                  value={generalSettings.keywords}
                  onChange={(e) => updateGeneralSetting("keywords", e.target.value)}
                />
                <p className="text-sm text-muted-foreground mt-1">Ключевые слова, разделенные запятыми.</p>
              </div>

              <div>
                <Label htmlFor="general-favicon">Favicon URL</Label>
                <Input
                  id="general-favicon"
                  value={generalSettings.favicon}
                  onChange={(e) => updateGeneralSetting("favicon", e.target.value)}
                />
                <p className="text-sm text-muted-foreground mt-1">
                  URL иконки сайта (обычно 16x16 или 32x32 пикселей).
                </p>
              </div>

              <div>
                <Label htmlFor="general-language">Язык сайта</Label>
                <Select
                  value={generalSettings.language}
                  onValueChange={(value) => updateGeneralSetting("language", value)}
                >
                  <SelectTrigger id="general-language">
                    <SelectValue placeholder="Выберите язык" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ru">Русский</SelectItem>
                    <SelectItem value="en">English</SelectItem>
                    <SelectItem value="es">Español</SelectItem>
                    <SelectItem value="fr">Français</SelectItem>
                    <SelectItem value="de">Deutsch</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-sm text-muted-foreground mt-1">Основной язык контента на сайте.</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics">
          <Card>
            <CardHeader>
              <CardTitle>Настройки аналитики</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="enable-ga"
                    checked={analyticsSettings.enableGoogleAnalytics}
                    onCheckedChange={(checked) => updateAnalyticsSetting("enableGoogleAnalytics", checked)}
                  />
                  <Label htmlFor="enable-ga">Включить Google Analytics</Label>
                </div>

                {analyticsSettings.enableGoogleAnalytics && (
                  <div>
                    <Label htmlFor="ga-id">Google Analytics ID</Label>
                    <Input
                      id="ga-id"
                      value={analyticsSettings.googleAnalyticsId}
                      onChange={(e) => updateAnalyticsSetting("googleAnalyticsId", e.target.value)}
                      placeholder="G-XXXXXXXXXX или UA-XXXXXXXX-X"
                    />
                    <p className="text-sm text-muted-foreground mt-1">Идентификатор отслеживания Google Analytics.</p>
                  </div>
                )}
              </div>

              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="enable-fb"
                    checked={analyticsSettings.enableFacebookPixel}
                    onCheckedChange={(checked) => updateAnalyticsSetting("enableFacebookPixel", checked)}
                  />
                  <Label htmlFor="enable-fb">Включить Facebook Pixel</Label>
                </div>

                {analyticsSettings.enableFacebookPixel && (
                  <div>
                    <Label htmlFor="fb-id">Facebook Pixel ID</Label>
                    <Input
                      id="fb-id"
                      value={analyticsSettings.facebookPixelId}
                      onChange={(e) => updateAnalyticsSetting("facebookPixelId", e.target.value)}
                      placeholder="XXXXXXXXXXXXXXXXXX"
                    />
                    <p className="text-sm text-muted-foreground mt-1">
                      Идентификатор пикселя Facebook для отслеживания конверсий.
                    </p>
                  </div>
                )}
              </div>

              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="enable-ym"
                    checked={analyticsSettings.enableYandexMetrika}
                    onCheckedChange={(checked) => updateAnalyticsSetting("enableYandexMetrika", checked)}
                  />
                  <Label htmlFor="enable-ym">Включить Яндекс.Метрику</Label>
                </div>

                {analyticsSettings.enableYandexMetrika && (
                  <div>
                    <Label htmlFor="ym-id">Яндекс.Метрика ID</Label>
                    <Input
                      id="ym-id"
                      value={analyticsSettings.yandexMetrikaId}
                      onChange={(e) => updateAnalyticsSetting("yandexMetrikaId", e.target.value)}
                      placeholder="XXXXXXXX"
                    />
                    <p className="text-sm text-muted-foreground mt-1">Номер счетчика Яндекс.Метрики.</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="social">
          <Card>
            <CardHeader>
              <CardTitle>Настройки для социальных сетей</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="og-title">Open Graph заголовок</Label>
                <Input
                  id="og-title"
                  value={socialSettings.ogTitle}
                  onChange={(e) => updateSocialSetting("ogTitle", e.target.value)}
                />
                <p className="text-sm text-muted-foreground mt-1">
                  Заголовок, который будет отображаться при шеринге в социальных сетях.
                </p>
              </div>

              <div>
                <Label htmlFor="og-description">Open Graph описание</Label>
                <Textarea
                  id="og-description"
                  value={socialSettings.ogDescription}
                  onChange={(e) => updateSocialSetting("ogDescription", e.target.value)}
                  rows={3}
                />
                <p className="text-sm text-muted-foreground mt-1">
                  Описание, которое будет отображаться при шеринге в социальных сетях.
                </p>
              </div>

              <div>
                <Label htmlFor="og-image">Open Graph изображение</Label>
                <Input
                  id="og-image"
                  value={socialSettings.ogImage}
                  onChange={(e) => updateSocialSetting("ogImage", e.target.value)}
                />
                <p className="text-sm text-muted-foreground mt-1">
                  URL изображения, которое будет отображаться при шеринге (рекомендуемый размер 1200x630).
                </p>
              </div>

              <div>
                <Label htmlFor="twitter-handle">Twitter аккаунт</Label>
                <Input
                  id="twitter-handle"
                  value={socialSettings.twitterHandle}
                  onChange={(e) => updateSocialSetting("twitterHandle", e.target.value)}
                />
                <p className="text-sm text-muted-foreground mt-1">
                  Имя пользователя Twitter с символом @ (например, @acmecompany).
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="advanced">
          <Card>
            <CardHeader>
              <CardTitle>Расширенные настройки</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="custom-css">Пользовательский CSS</Label>
                <Textarea
                  id="custom-css"
                  value={advancedSettings.customCss}
                  onChange={(e) => updateAdvancedSetting("customCss", e.target.value)}
                  rows={5}
                  className="font-mono text-sm"
                />
                <p className="text-sm text-muted-foreground mt-1">
                  Дополнительные CSS стили, которые будут добавлены на страницу.
                </p>
              </div>

              <div>
                <Label htmlFor="custom-js">Пользовательский JavaScript</Label>
                <Textarea
                  id="custom-js"
                  value={advancedSettings.customJs}
                  onChange={(e) => updateAdvancedSetting("customJs", e.target.value)}
                  rows={5}
                  className="font-mono text-sm"
                />
                <p className="text-sm text-muted-foreground mt-1">
                  Дополнительный JavaScript код, который будет добавлен на страницу.
                </p>
              </div>

              <div>
                <Label htmlFor="robots-txt">Содержимое robots.txt</Label>
                <Textarea
                  id="robots-txt"
                  value={advancedSettings.robotsTxt}
                  onChange={(e) => updateAdvancedSetting("robotsTxt", e.target.value)}
                  rows={3}
                  className="font-mono text-sm"
                />
                <p className="text-sm text-muted-foreground mt-1">Содержимое файла robots.txt для поисковых роботов.</p>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="sitemap-enabled"
                  checked={advancedSettings.sitemapEnabled}
                  onCheckedChange={(checked) => updateAdvancedSetting("sitemapEnabled", checked)}
                />
                <Label htmlFor="sitemap-enabled">Автоматически генерировать sitemap.xml</Label>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="footer">
          <Tabs defaultValue="sections" className="mb-6">
            <TabsList>
              <TabsTrigger value="sections">Разделы и ссылки</TabsTrigger>
              <TabsTrigger value="social">Социальные сети</TabsTrigger>
              <TabsTrigger value="styles">Стили</TabsTrigger>
              <TabsTrigger value="copyright">Копирайт и лого</TabsTrigger>
            </TabsList>

            <TabsContent value="sections">
              <Card className="mb-4">
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <CardTitle>Разделы футера</CardTitle>
                    <Button variant="outline" size="sm" onClick={addSection}>
                      <Plus className="mr-2 h-4 w-4" />
                      Добавить раздел
                    </Button>
                  </div>
                </CardHeader>
              </Card>

              {footerSections.map((section) => (
                <Card key={section.id} className="mb-4">
                  <CardHeader>
                    <div className="flex justify-between items-center">
                      <div className="flex-1 mr-4">
                        <Input
                          value={section.title}
                          onChange={(e) => updateSectionTitle(section.id, e.target.value)}
                          className="font-bold"
                        />
                      </div>
                      <Button variant="destructive" size="sm" onClick={() => removeSection(section.id)}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {section.links.map((link) => (
                        <div key={link.id} className="flex items-center gap-2">
                          <div className="flex-1">
                            <Input
                              value={link.text}
                              onChange={(e) => updateLink(section.id, link.id, "text", e.target.value)}
                              placeholder="Текст ссылки"
                            />
                          </div>
                          <div className="flex-1">
                            <Input
                              value={link.url}
                              onChange={(e) => updateLink(section.id, link.id, "url", e.target.value)}
                              placeholder="URL"
                            />
                          </div>
                          <Button variant="destructive" size="sm" onClick={() => removeLink(section.id, link.id)}>
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                      <Button variant="outline" size="sm" onClick={() => addLink(section.id)} className="w-full">
                        <Plus className="mr-2 h-4 w-4" />
                        Добавить ссылку
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </TabsContent>

            <TabsContent value="social">
              <Card className="mb-4">
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <CardTitle>Социальные сети</CardTitle>
                    <Button variant="outline" size="sm" onClick={addSocialLink}>
                      <Plus className="mr-2 h-4 w-4" />
                      Добавить соцсеть
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {footerSocialLinks.map((link) => (
                      <div key={link.id} className="flex items-center gap-2">
                        <div className="flex-1">
                          <Select value={link.icon} onValueChange={(value) => updateSocialLink(link.id, "icon", value)}>
                            <SelectTrigger>
                              <SelectValue placeholder="Выберите иконку" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="Facebook">Facebook</SelectItem>
                              <SelectItem value="Twitter">Twitter</SelectItem>
                              <SelectItem value="Instagram">Instagram</SelectItem>
                              <SelectItem value="Linkedin">LinkedIn</SelectItem>
                              <SelectItem value="Youtube">YouTube</SelectItem>
                              <SelectItem value="Github">GitHub</SelectItem>
                              <SelectItem value="Mail">Email</SelectItem>
                              <SelectItem value="Globe">Website</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="flex-1">
                          <Input
                            value={link.platform}
                            onChange={(e) => updateSocialLink(link.id, "platform", e.target.value)}
                            placeholder="Название"
                          />
                        </div>
                        <div className="flex-1">
                          <Input
                            value={link.url}
                            onChange={(e) => updateSocialLink(link.id, "url", e.target.value)}
                            placeholder="URL"
                          />
                        </div>
                        <Button variant="destructive" size="sm" onClick={() => removeSocialLink(link.id)}>
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="styles">
              <Card>
                <CardHeader>
                  <CardTitle>Стили футера</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 md:grid-cols-2">
                    <div>
                      <Label htmlFor="footer-bg">Цвет фона</Label>
                      <Input
                        id="footer-bg"
                        type="color"
                        value={footerStyles.background}
                        onChange={(e) => updateFooterStyle("background", e.target.value)}
                        className="h-10"
                      />
                    </div>
                    <div>
                      <Label htmlFor="footer-text">Цвет текста</Label>
                      <Input
                        id="footer-text"
                        type="color"
                        value={footerStyles.textColor}
                        onChange={(e) => updateFooterStyle("textColor", e.target.value)}
                        className="h-10"
                      />
                    </div>
                    <div>
                      <Label htmlFor="footer-link">Цвет ссылок</Label>
                      <Input
                        id="footer-link"
                        type="color"
                        value={footerStyles.linkColor}
                        onChange={(e) => updateFooterStyle("linkColor", e.target.value)}
                        className="h-10"
                      />
                    </div>
                    <div>
                      <Label htmlFor="footer-link-hover">Цвет ссылок при наведении</Label>
                      <Input
                        id="footer-link-hover"
                        type="color"
                        value={footerStyles.linkHoverColor}
                        onChange={(e) => updateFooterStyle("linkHoverColor", e.target.value)}
                        className="h-10"
                      />
                    </div>
                    <div>
                      <Label htmlFor="footer-border">Цвет границы</Label>
                      <Input
                        id="footer-border"
                        type="color"
                        value={footerStyles.borderColor}
                        onChange={(e) => updateFooterStyle("borderColor", e.target.value)}
                        className="h-10"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="copyright">
              <Card>
                <CardHeader>
                  <CardTitle>Копирайт и логотип</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="footer-copyright">Текст копирайта</Label>
                    <Input
                      id="footer-copyright"
                      value={footerCopyright}
                      onChange={(e) => updateFooterCopyright(e.target.value)}
                      placeholder="© 2023 Название компании. Все права защищены."
                    />
                  </div>

                  <div className="flex items-center space-x-2 mt-6">
                    <Switch id="show-logo" checked={footerLogo.enabled} onCheckedChange={toggleFooterLogo} />
                    <Label htmlFor="show-logo">Показывать логотип в футере</Label>
                  </div>

                  {footerLogo.enabled && (
                    <div>
                      <Label htmlFor="footer-logo-url">URL логотипа</Label>
                      <Input
                        id="footer-logo-url"
                        value={footerLogo.url}
                        onChange={(e) => updateFooterLogo(e.target.value)}
                        placeholder="/logo.svg"
                      />
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          <Card>
            <CardHeader>
              <CardTitle>Предпросмотр футера</CardTitle>
            </CardHeader>
            <CardContent>
              <div
                className="border rounded-lg p-6 grid gap-8 sm:grid-cols-2 md:grid-cols-4"
                style={{
                  backgroundColor: footerStyles.background,
                  color: footerStyles.textColor,
                  borderColor: footerStyles.borderColor,
                }}
              >
                {footerLogo.enabled && (
                  <div className="md:col-span-4 mb-4">
                    <img src={footerLogo.url || "/placeholder.svg"} alt="Logo" className="h-8" />
                  </div>
                )}

                {footerSections.map((section) => (
                  <div key={section.id} className="space-y-3">
                    <h3 className="font-medium">{section.title}</h3>
                    <ul className="space-y-2">
                      {section.links.map((link) => (
                        <li key={link.id}>
                          <a
                            href={link.url}
                            className="text-sm transition-colors"
                            style={{ color: footerStyles.linkColor }}
                          >
                            {link.text}
                          </a>
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}

                <div
                  className="md:col-span-4 mt-6 pt-6 flex flex-col sm:flex-row justify-between items-center gap-4"
                  style={{ borderTop: `1px solid ${footerStyles.borderColor}` }}
                >
                  <p className="text-sm">{footerCopyright}</p>

                  {footerSocialLinks.length > 0 && (
                    <div className="flex gap-4">
                      {footerSocialLinks.map((link) => {
                        const IconComponent = link.icon as any
                        return (
                          <a
                            key={link.id}
                            href={link.url}
                            title={link.platform}
                            style={{ color: footerStyles.linkColor }}
                          >
                            <span className="sr-only">{link.platform}</span>
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="24"
                              height="24"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              className="h-5 w-5"
                            >
                              {link.icon === "Facebook" && (
                                <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z" />
                              )}
                              {link.icon === "Twitter" && (
                                <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z" />
                              )}
                              {link.icon === "Instagram" && (
                                <>
                                  <rect width="20" height="20" x="2" y="2" rx="5" ry="5" />
                                  <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z" />
                                  <line x1="17.5" x2="17.51" y1="6.5" y2="6.5" />
                                </>
                              )}
                              {link.icon === "Linkedin" && (
                                <>
                                  <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z" />
                                  <rect width="4" height="12" x="2" y="9" />
                                  <circle cx="4" cy="4" r="2" />
                                </>
                              )}
                              {link.icon === "Youtube" && (
                                <>
                                  <path d="M12 19c-2.3 0-6.4-.2-8.1-.6-.7-.2-1.2-.7-1.4-1.4-.3-1.1-.5-3.4-.5-5s.2-3.9.5-5c.2-.7.7-1.2 1.4-1.4C5.6 5.2 9.7 5 12 5s6.4.2 8.1.6c.7.2 1.2.7 1.4 1.4.3 1.1.5 3.4.5 5s-.2 3.9-.5 5c-.2.7-.7 1.2-1.4 1.4-1.7.4-5.8.6-8.1.6 0 0 0 0 0 0z" />
                                  <polygon points="10 15 15 12 10 9" />
                                </>
                              )}
                              {link.icon === "Github" && (
                                <path d="M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4" />
                              )}
                              {link.icon === "Mail" && (
                                <>
                                  <rect width="20" height="16" x="2" y="4" rx="2" />
                                  <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7" />
                                </>
                              )}
                              {link.icon === "Globe" && (
                                <>
                                  <circle cx="12" cy="12" r="10" />
                                  <line x1="2" x2="22" y1="12" y2="12" />
                                  <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z" />
                                </>
                              )}
                            </svg>
                          </a>
                        )
                      })}
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="flex justify-end">
        <Button onClick={saveChanges}>Сохранить все настройки</Button>
      </div>
    </div>
  )
}
