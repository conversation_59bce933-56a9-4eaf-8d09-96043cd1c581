"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { Trash2, Plus, Star } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface Testimonial {
  id: number
  name: string
  position: string
  company: string
  content: string
  rating: number
  avatar: string
}

export default function AdminTestimonialsPage() {
  const { toast } = useToast()
  const [testimonials, setTestimonials] = useState<Testimonial[]>([
    {
      id: 1,
      name: "<PERSON>",
      position: "Marketing Director",
      company: "Tech Corp",
      content: "This platform has transformed how our marketing team operates.",
      rating: 5,
      avatar: "/placeholder.svg?height=40&width=40&query=professional%20woman%20portrait",
    },
    {
      id: 2,
      name: "<PERSON>",
      position: "CTO",
      company: "StartupXYZ",
      content: "The security features are top-notch, and the scalability has allowed us to grow.",
      rating: 5,
      avatar: "/placeholder.svg?height=40&width=40&query=professional%20man%20portrait",
    },
  ])

  const [newTestimonial, setNewTestimonial] = useState({
    name: "",
    position: "",
    company: "",
    content: "",
    rating: 5,
    avatar: "",
  })

  const addTestimonial = () => {
    if (newTestimonial.name && newTestimonial.content) {
      setTestimonials([
        ...testimonials,
        {
          id: Date.now(),
          ...newTestimonial,
        },
      ])
      setNewTestimonial({
        name: "",
        position: "",
        company: "",
        content: "",
        rating: 5,
        avatar: "",
      })
      toast({
        title: "Отзыв добавлен",
        description: "Новый отзыв успешно добавлен",
      })
    }
  }

  const deleteTestimonial = (id: number) => {
    setTestimonials(testimonials.filter((t) => t.id !== id))
    toast({
      title: "Отзыв удален",
      description: "Отзыв успешно удален",
    })
  }

  return (
    <div>
      <h1 className="text-3xl font-bold mb-8">Управление отзывами</h1>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Добавить новый отзыв</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <Label htmlFor="name">Имя</Label>
              <Input
                id="name"
                value={newTestimonial.name}
                onChange={(e) => setNewTestimonial({ ...newTestimonial, name: e.target.value })}
                placeholder="Имя клиента"
              />
            </div>
            <div>
              <Label htmlFor="position">Должность</Label>
              <Input
                id="position"
                value={newTestimonial.position}
                onChange={(e) => setNewTestimonial({ ...newTestimonial, position: e.target.value })}
                placeholder="Должность"
              />
            </div>
            <div>
              <Label htmlFor="company">Компания</Label>
              <Input
                id="company"
                value={newTestimonial.company}
                onChange={(e) => setNewTestimonial({ ...newTestimonial, company: e.target.value })}
                placeholder="Название компании"
              />
            </div>
            <div>
              <Label htmlFor="rating">Рейтинг</Label>
              <Select
                value={newTestimonial.rating.toString()}
                onValueChange={(value) => setNewTestimonial({ ...newTestimonial, rating: Number.parseInt(value) })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5 звезд</SelectItem>
                  <SelectItem value="4">4 звезды</SelectItem>
                  <SelectItem value="3">3 звезды</SelectItem>
                  <SelectItem value="2">2 звезды</SelectItem>
                  <SelectItem value="1">1 звезда</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="md:col-span-2">
              <Label htmlFor="content">Отзыв</Label>
              <Textarea
                id="content"
                value={newTestimonial.content}
                onChange={(e) => setNewTestimonial({ ...newTestimonial, content: e.target.value })}
                placeholder="Текст отзыва"
                rows={3}
              />
            </div>
            <div className="md:col-span-2">
              <Label htmlFor="avatar">URL аватара</Label>
              <Input
                id="avatar"
                value={newTestimonial.avatar}
                onChange={(e) => setNewTestimonial({ ...newTestimonial, avatar: e.target.value })}
                placeholder="URL изображения"
              />
            </div>
            <div className="md:col-span-2">
              <Button onClick={addTestimonial} className="w-full">
                <Plus className="mr-2 h-4 w-4" />
                Добавить отзыв
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid gap-4">
        <h2 className="text-xl font-semibold">Текущие отзывы</h2>
        {testimonials.map((testimonial) => (
          <Card key={testimonial.id}>
            <CardContent className="flex items-start justify-between p-4">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <h3 className="font-semibold">{testimonial.name}</h3>
                  <div className="flex text-yellow-500">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="h-4 w-4 fill-current" />
                    ))}
                  </div>
                </div>
                <p className="text-sm text-muted-foreground">
                  {testimonial.position} в {testimonial.company}
                </p>
                <p className="mt-2">{testimonial.content}</p>
              </div>
              <Button variant="destructive" size="sm" onClick={() => deleteTestimonial(testimonial.id)}>
                <Trash2 className="h-4 w-4" />
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
