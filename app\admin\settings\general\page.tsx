"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export default function GeneralSettingsPage() {
  const { toast } = useToast()

  const [siteSettings, setSiteSettings] = useState({
    siteName: "ACME",
    adminEmail: "<EMAIL>",
    timezone: "Europe/Moscow",
    dateFormat: "DD.MM.YYYY",
    timeFormat: "24",
  })

  const [userSettings, setUserSettings] = useState({
    allowRegistration: true,
    defaultUserRole: "subscriber",
    requireEmailVerification: true,
    sessionTimeout: "1440", // 24 hours in minutes
  })

  const [securitySettings, setSecuritySettings] = useState({
    enableCaptcha: true,
    maxLoginAttempts: "5",
    lockoutTime: "30", // minutes
    forceStrongPasswords: true,
    enableTwoFactor: false,
  })

  const updateSiteSetting = (key: keyof typeof siteSettings, value: string) => {
    setSiteSettings({
      ...siteSettings,
      [key]: value,
    })
  }

  const updateUserSetting = (key: keyof typeof userSettings, value: string | boolean) => {
    setUserSettings({
      ...userSettings,
      [key]: value,
    })
  }

  const updateSecuritySetting = (key: keyof typeof securitySettings, value: string | boolean) => {
    setSecuritySettings({
      ...securitySettings,
      [key]: value,
    })
  }

  const saveChanges = () => {
    // В реальном приложении здесь был бы API запрос для сохранения данных
    toast({
      title: "Настройки сохранены",
      description: "Общие настройки успешно обновлены",
    })
  }

  return (
    <div>
      <h1 className="text-3xl font-bold mb-8">Общие настройки</h1>

      <Tabs defaultValue="site" className="mb-8">
        <TabsList>
          <TabsTrigger value="site">Сайт</TabsTrigger>
          <TabsTrigger value="users">Пользователи</TabsTrigger>
          <TabsTrigger value="security">Безопасность</TabsTrigger>
        </TabsList>

        <TabsContent value="site">
          <Card>
            <CardHeader>
              <CardTitle>Настройки сайта</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="site-name">Название сайта</Label>
                <Input
                  id="site-name"
                  value={siteSettings.siteName}
                  onChange={(e) => updateSiteSetting("siteName", e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="admin-email">Email администратора</Label>
                <Input
                  id="admin-email"
                  type="email"
                  value={siteSettings.adminEmail}
                  onChange={(e) => updateSiteSetting("adminEmail", e.target.value)}
                />
                <p className="text-sm text-muted-foreground mt-1">
                  Используется для системных уведомлений и восстановления доступа.
                </p>
              </div>

              <div>
                <Label htmlFor="timezone">Часовой пояс</Label>
                <Select value={siteSettings.timezone} onValueChange={(value) => updateSiteSetting("timezone", value)}>
                  <SelectTrigger id="timezone">
                    <SelectValue placeholder="Выберите часовой пояс" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Europe/Moscow">Москва (GMT+3)</SelectItem>
                    <SelectItem value="Europe/London">Лондон (GMT+0)</SelectItem>
                    <SelectItem value="America/New_York">Нью-Йорк (GMT-5)</SelectItem>
                    <SelectItem value="Asia/Tokyo">Токио (GMT+9)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="date-format">Формат даты</Label>
                <Select
                  value={siteSettings.dateFormat}
                  onValueChange={(value) => updateSiteSetting("dateFormat", value)}
                >
                  <SelectTrigger id="date-format">
                    <SelectValue placeholder="Выберите формат даты" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="DD.MM.YYYY">DD.MM.YYYY (31.12.2023)</SelectItem>
                    <SelectItem value="MM/DD/YYYY">MM/DD/YYYY (12/31/2023)</SelectItem>
                    <SelectItem value="YYYY-MM-DD">YYYY-MM-DD (2023-12-31)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="time-format">Формат времени</Label>
                <Select
                  value={siteSettings.timeFormat}
                  onValueChange={(value) => updateSiteSetting("timeFormat", value)}
                >
                  <SelectTrigger id="time-format">
                    <SelectValue placeholder="Выберите формат времени" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="24">24-часовой (14:30)</SelectItem>
                    <SelectItem value="12">12-часовой (2:30 PM)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="users">
          <Card>
            <CardHeader>
              <CardTitle>Настройки пользователей</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="allow-registration"
                  checked={userSettings.allowRegistration}
                  onCheckedChange={(checked) => updateUserSetting("allowRegistration", checked)}
                />
                <Label htmlFor="allow-registration">Разрешить регистрацию новых пользователей</Label>
              </div>

              <div>
                <Label htmlFor="default-role">Роль по умолчанию для новых пользователей</Label>
                <Select
                  value={userSettings.defaultUserRole}
                  onValueChange={(value) => updateUserSetting("defaultUserRole", value)}
                >
                  <SelectTrigger id="default-role">
                    <SelectValue placeholder="Выберите роль" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="subscriber">Подписчик</SelectItem>
                    <SelectItem value="contributor">Контрибьютор</SelectItem>
                    <SelectItem value="author">Автор</SelectItem>
                    <SelectItem value="editor">Редактор</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="email-verification"
                  checked={userSettings.requireEmailVerification}
                  onCheckedChange={(checked) => updateUserSetting("requireEmailVerification", checked)}
                />
                <Label htmlFor="email-verification">Требовать подтверждение email</Label>
              </div>

              <div>
                <Label htmlFor="session-timeout">Время жизни сессии (в минутах)</Label>
                <Input
                  id="session-timeout"
                  type="number"
                  value={userSettings.sessionTimeout}
                  onChange={(e) => updateUserSetting("sessionTimeout", e.target.value)}
                />
                <p className="text-sm text-muted-foreground mt-1">
                  Время в минутах до автоматического выхода из системы (1440 = 24 часа).
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security">
          <Card>
            <CardHeader>
              <CardTitle>Настройки безопасности</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="enable-captcha"
                  checked={securitySettings.enableCaptcha}
                  onCheckedChange={(checked) => updateSecuritySetting("enableCaptcha", checked)}
                />
                <Label htmlFor="enable-captcha">Включить CAPTCHA при входе</Label>
              </div>

              <div>
                <Label htmlFor="max-login-attempts">Максимальное количество попыток входа</Label>
                <Input
                  id="max-login-attempts"
                  type="number"
                  value={securitySettings.maxLoginAttempts}
                  onChange={(e) => updateSecuritySetting("maxLoginAttempts", e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="lockout-time">Время блокировки (в минутах)</Label>
                <Input
                  id="lockout-time"
                  type="number"
                  value={securitySettings.lockoutTime}
                  onChange={(e) => updateSecuritySetting("lockoutTime", e.target.value)}
                />
                <p className="text-sm text-muted-foreground mt-1">
                  Время блокировки после превышения максимального количества попыток входа.
                </p>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="strong-passwords"
                  checked={securitySettings.forceStrongPasswords}
                  onCheckedChange={(checked) => updateSecuritySetting("forceStrongPasswords", checked)}
                />
                <Label htmlFor="strong-passwords">Требовать сложные пароли</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="two-factor"
                  checked={securitySettings.enableTwoFactor}
                  onCheckedChange={(checked) => updateSecuritySetting("enableTwoFactor", checked)}
                />
                <Label htmlFor="two-factor">Включить двухфакторную аутентификацию</Label>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="flex justify-end">
        <Button onClick={saveChanges}>Сохранить настройки</Button>
      </div>
    </div>
  )
}
