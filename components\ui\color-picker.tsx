"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { cn } from "@/lib/utils"

interface ColorPickerProps {
  value: string
  onChange: (value: string) => void
  label?: string
}

export function ColorPicker({ value, onChange, label }: ColorPickerProps) {
  const [color, setColor] = useState(value || "#ffffff")
  const [inputValue, setInputValue] = useState(value || "#ffffff")
  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    setColor(value)
    setInputValue(value)
  }, [value])

  const handleColorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newColor = e.target.value
    setColor(newColor)
    onChange(newColor)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    setInputValue(newValue)

    // Validate if it's a proper hex color
    if (/^#([0-9A-F]{3}){1,2}$/i.test(newValue)) {
      setColor(newValue)
      onChange(newValue)
    }
  }

  const handleInputBlur = () => {
    // Reset to the current color if invalid input
    if (!/^#([0-9A-F]{3}){1,2}$/i.test(inputValue)) {
      setInputValue(color)
    }
  }

  return (
    <div className="space-y-2">
      {label && <Label>{label}</Label>}
      <div className="flex items-center gap-2">
        <Popover>
          <PopoverTrigger asChild>
            <button
              type="button"
              className={cn(
                "h-8 w-8 rounded-md border border-input",
                "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring",
              )}
              style={{ backgroundColor: color }}
              aria-label="Pick a color"
            />
          </PopoverTrigger>
          <PopoverContent className="w-auto p-3">
            <input
              ref={inputRef}
              type="color"
              value={color}
              onChange={handleColorChange}
              className="h-32 w-32 cursor-pointer appearance-none border-0 bg-transparent p-0"
            />
          </PopoverContent>
        </Popover>
        <Input
          value={inputValue}
          onChange={handleInputChange}
          onBlur={handleInputBlur}
          className="w-28 font-mono uppercase"
          maxLength={7}
        />
      </div>
    </div>
  )
}
