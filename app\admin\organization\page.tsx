"use client"

import { useState, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { FileUpload } from "@/components/ui/file-upload"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Building2, Users, MapPin, Phone, Mail, Globe, Plus, Trash2, Edit, User } from "lucide-react"
import { OrganizationChart } from "@/components/organization-chart"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/dialog"
import { ExportChart } from "@/components/export-chart"

interface Department {
  id: string
  name: string
  manager: string
  employees: number
  location: string
}

interface Employee {
  id: string
  name: string
  position: string
  department: string
  email: string
  phone: string
  status: "active" | "vacation" | "sick" | "remote"
  joinDate: string
}

interface Location {
  id: string
  name: string
  address: string
  city: string
  country: string
  employees: number
  isHeadquarters: boolean
}

export default function OrganizationPage() {
  const { toast } = useToast()

  // Организационные данные
  const [orgInfo, setOrgInfo] = useState({
    name: "ACME Corporation",
    legalName: "ACME Corporation LLC",
    taxId: "1234567890",
    industry: "Technology",
    founded: "2010-01-15",
    website: "https://acme.example.com",
    email: "<EMAIL>",
    phone: "+****************",
    address: "123 Main Street",
    city: "San Francisco",
    state: "CA",
    postalCode: "94105",
    country: "United States",
    description:
      "ACME Corporation is a leading technology company specializing in innovative solutions for businesses of all sizes.",
    logoUrl: "/placeholder.svg?height=100&width=100&query=company%20logo",
  })

  // Отделы
  const [departments, setDepartments] = useState<Department[]>([
    {
      id: "dept-1",
      name: "Разработка",
      manager: "Иван Петров",
      employees: 12,
      location: "Главный офис",
    },
    {
      id: "dept-2",
      name: "Маркетинг",
      manager: "Анна Сидорова",
      employees: 8,
      location: "Главный офис",
    },
    {
      id: "dept-3",
      name: "Продажи",
      manager: "Алексей Иванов",
      employees: 15,
      location: "Восточный филиал",
    },
    {
      id: "dept-4",
      name: "Поддержка",
      manager: "Мария Кузнецова",
      employees: 10,
      location: "Удаленно",
    },
  ])

  // Сотрудники
  const [employees, setEmployees] = useState<Employee[]>([
    {
      id: "emp-1",
      name: "Иван Петров",
      position: "Технический директор",
      department: "Разработка",
      email: "<EMAIL>",
      phone: "+7 (*************",
      status: "active",
      joinDate: "2015-03-10",
    },
    {
      id: "emp-2",
      name: "Анна Сидорова",
      position: "Директор по маркетингу",
      department: "Маркетинг",
      email: "<EMAIL>",
      phone: "+7 (*************",
      status: "active",
      joinDate: "2016-05-15",
    },
    {
      id: "emp-3",
      name: "Алексей Иванов",
      position: "Руководитель отдела продаж",
      department: "Продажи",
      email: "<EMAIL>",
      phone: "+7 (*************",
      status: "vacation",
      joinDate: "2017-02-20",
    },
    {
      id: "emp-4",
      name: "Мария Кузнецова",
      position: "Руководитель службы поддержки",
      department: "Поддержка",
      email: "<EMAIL>",
      phone: "+7 (*************",
      status: "remote",
      joinDate: "2018-07-05",
    },
  ])

  // Локации
  const [locations, setLocations] = useState<Location[]>([
    {
      id: "loc-1",
      name: "Главный офис",
      address: "ул. Ленина, 123",
      city: "Москва",
      country: "Россия",
      employees: 25,
      isHeadquarters: true,
    },
    {
      id: "loc-2",
      name: "Восточный филиал",
      address: "ул. Пушкина, 45",
      city: "Владивосток",
      country: "Россия",
      employees: 15,
      isHeadquarters: false,
    },
    {
      id: "loc-3",
      name: "Европейский офис",
      address: "Hauptstrasse 1",
      city: "Берлин",
      country: "Германия",
      employees: 8,
      isHeadquarters: false,
    },
  ])

  // Состояния для диалогов
  const [showNewDepartmentDialog, setShowNewDepartmentDialog] = useState(false)
  const [showNewEmployeeDialog, setShowNewEmployeeDialog] = useState(false)
  const [showNewLocationDialog, setShowNewLocationDialog] = useState(false)

  // Новые записи
  const [newDepartment, setNewDepartment] = useState({
    name: "",
    manager: "",
    location: "",
  })

  const [newEmployee, setNewEmployee] = useState({
    name: "",
    position: "",
    department: "",
    email: "",
    phone: "",
  })

  const [newLocation, setNewLocation] = useState({
    name: "",
    address: "",
    city: "",
    country: "",
    isHeadquarters: false,
  })

  const chartRef = useRef<HTMLDivElement>(null)
  const [chartLayout, setChartLayout] = useState<"hierarchical" | "orgchart" | "network">("hierarchical")

  // Обработчики для организационной информации
  const updateOrgInfo = (key: keyof typeof orgInfo, value: string) => {
    setOrgInfo({
      ...orgInfo,
      [key]: value,
    })
  }

  const handleLogoChange = (file: File | null) => {
    if (file) {
      const reader = new FileReader()
      reader.onload = () => {
        updateOrgInfo("logoUrl", reader.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  // Обработчики для отделов
  const addDepartment = () => {
    if (newDepartment.name && newDepartment.location) {
      const department: Department = {
        id: `dept-${Date.now()}`,
        name: newDepartment.name,
        manager: newDepartment.manager || "Не назначен",
        employees: 0,
        location: newDepartment.location,
      }
      setDepartments([...departments, department])
      setNewDepartment({ name: "", manager: "", location: "" })
      setShowNewDepartmentDialog(false)
      toast({
        title: "Отдел создан",
        description: `Отдел "${department.name}" успешно создан`,
      })
    }
  }

  const deleteDepartment = (id: string) => {
    setDepartments(departments.filter((dept) => dept.id !== id))
    toast({
      title: "Отдел удален",
      description: "Отдел успешно удален",
    })
  }

  // Обработчики для сотрудников
  const addEmployee = () => {
    if (newEmployee.name && newEmployee.position && newEmployee.department) {
      const employee: Employee = {
        id: `emp-${Date.now()}`,
        name: newEmployee.name,
        position: newEmployee.position,
        department: newEmployee.department,
        email: newEmployee.email || "",
        phone: newEmployee.phone || "",
        status: "active",
        joinDate: new Date().toISOString().split("T")[0],
      }
      setEmployees([...employees, employee])

      // Обновляем количество сотрудников в отделе
      setDepartments(
        departments.map((dept) =>
          dept.name === employee.department ? { ...dept, employees: dept.employees + 1 } : dept,
        ),
      )

      setNewEmployee({ name: "", position: "", department: "", email: "", phone: "" })
      setShowNewEmployeeDialog(false)
      toast({
        title: "Сотрудник добавлен",
        description: `Сотрудник "${employee.name}" успешно добавлен`,
      })
    }
  }

  const deleteEmployee = (id: string) => {
    const employee = employees.find((emp) => emp.id === id)
    if (employee) {
      // Обновляем количество сотрудников в отделе
      setDepartments(
        departments.map((dept) =>
          dept.name === employee.department ? { ...dept, employees: Math.max(0, dept.employees - 1) } : dept,
        ),
      )

      setEmployees(employees.filter((emp) => emp.id !== id))
      toast({
        title: "Сотрудник удален",
        description: "Сотрудник успешно удален",
      })
    }
  }

  const updateEmployeeStatus = (id: string, status: Employee["status"]) => {
    setEmployees(employees.map((emp) => (emp.id === id ? { ...emp, status } : emp)))
    toast({
      title: "Статус обновлен",
      description: "Статус сотрудника успешно обновлен",
    })
  }

  // Обработчики для локаций
  const addLocation = () => {
    if (newLocation.name && newLocation.address && newLocation.city && newLocation.country) {
      const location: Location = {
        id: `loc-${Date.now()}`,
        name: newLocation.name,
        address: newLocation.address,
        city: newLocation.city,
        country: newLocation.country,
        employees: 0,
        isHeadquarters: newLocation.isHeadquarters,
      }

      // Если новая локация - штаб-квартира, обновляем все остальные
      if (newLocation.isHeadquarters) {
        setLocations(locations.map((loc) => ({ ...loc, isHeadquarters: false })).concat(location))
      } else {
        setLocations([...locations, location])
      }

      setNewLocation({ name: "", address: "", city: "", country: "", isHeadquarters: false })
      setShowNewLocationDialog(false)
      toast({
        title: "Локация добавлена",
        description: `Локация "${location.name}" успешно добавлена`,
      })
    }
  }

  const deleteLocation = (id: string) => {
    const location = locations.find((loc) => loc.id === id)
    if (location && !location.isHeadquarters) {
      setLocations(locations.filter((loc) => loc.id !== id))
      toast({
        title: "Локация удалена",
        description: "Локация успешно удалена",
      })
    } else if (location?.isHeadquarters) {
      toast({
        title: "Ошибка",
        description: "Невозможно удалить штаб-квартиру",
      })
    }
  }

  const setHeadquarters = (id: string) => {
    setLocations(
      locations.map((loc) => ({
        ...loc,
        isHeadquarters: loc.id === id,
      })),
    )
    toast({
      title: "Штаб-квартира обновлена",
      description: "Штаб-квартира успешно обновлена",
    })
  }

  // Сохранение всех изменений
  const saveChanges = () => {
    toast({
      title: "Изменения сохранены",
      description: "Все изменения успешно сохранены",
    })
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold">Организация</h1>
          <p className="text-muted-foreground mt-2">Управление структурой и данными организации</p>
        </div>
        <Building2 className="h-8 w-8 text-muted-foreground" />
      </div>

      <Tabs defaultValue="info" className="space-y-4">
        <TabsList>
          <TabsTrigger value="info">Информация</TabsTrigger>
          <TabsTrigger value="departments">Отделы</TabsTrigger>
          <TabsTrigger value="employees">Сотрудники</TabsTrigger>
          <TabsTrigger value="locations">Локации</TabsTrigger>
          <TabsTrigger value="structure">Структура</TabsTrigger>
        </TabsList>

        <TabsContent value="info" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Основная информация</CardTitle>
              <CardDescription>Основные данные о вашей организации</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex flex-col md:flex-row gap-8">
                <div className="md:w-1/3">
                  <Label htmlFor="org-logo">Логотип организации</Label>
                  <FileUpload id="org-logo" onFileChange={handleLogoChange} previewUrl={orgInfo.logoUrl} />
                </div>
                <div className="md:w-2/3 space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="org-name">Название организации</Label>
                      <Input
                        id="org-name"
                        value={orgInfo.name}
                        onChange={(e) => updateOrgInfo("name", e.target.value)}
                      />
                    </div>
                    <div>
                      <Label htmlFor="org-legal-name">Юридическое название</Label>
                      <Input
                        id="org-legal-name"
                        value={orgInfo.legalName}
                        onChange={(e) => updateOrgInfo("legalName", e.target.value)}
                      />
                    </div>
                    <div>
                      <Label htmlFor="org-tax-id">ИНН/КПП</Label>
                      <Input
                        id="org-tax-id"
                        value={orgInfo.taxId}
                        onChange={(e) => updateOrgInfo("taxId", e.target.value)}
                      />
                    </div>
                    <div>
                      <Label htmlFor="org-industry">Отрасль</Label>
                      <Input
                        id="org-industry"
                        value={orgInfo.industry}
                        onChange={(e) => updateOrgInfo("industry", e.target.value)}
                      />
                    </div>
                    <div>
                      <Label htmlFor="org-founded">Дата основания</Label>
                      <Input
                        id="org-founded"
                        type="date"
                        value={orgInfo.founded}
                        onChange={(e) => updateOrgInfo("founded", e.target.value)}
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="org-website">Веб-сайт</Label>
                  <div className="flex">
                    <Globe className="mr-2 h-4 w-4 mt-3 text-muted-foreground" />
                    <Input
                      id="org-website"
                      value={orgInfo.website}
                      onChange={(e) => updateOrgInfo("website", e.target.value)}
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="org-email">Email</Label>
                  <div className="flex">
                    <Mail className="mr-2 h-4 w-4 mt-3 text-muted-foreground" />
                    <Input
                      id="org-email"
                      type="email"
                      value={orgInfo.email}
                      onChange={(e) => updateOrgInfo("email", e.target.value)}
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="org-phone">Телефон</Label>
                  <div className="flex">
                    <Phone className="mr-2 h-4 w-4 mt-3 text-muted-foreground" />
                    <Input
                      id="org-phone"
                      value={orgInfo.phone}
                      onChange={(e) => updateOrgInfo("phone", e.target.value)}
                    />
                  </div>
                </div>
              </div>

              <div>
                <Label htmlFor="org-address">Адрес</Label>
                <div className="flex">
                  <MapPin className="mr-2 h-4 w-4 mt-3 text-muted-foreground" />
                  <Input
                    id="org-address"
                    value={orgInfo.address}
                    onChange={(e) => updateOrgInfo("address", e.target.value)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <Label htmlFor="org-city">Город</Label>
                  <Input id="org-city" value={orgInfo.city} onChange={(e) => updateOrgInfo("city", e.target.value)} />
                </div>
                <div>
                  <Label htmlFor="org-state">Область/Регион</Label>
                  <Input
                    id="org-state"
                    value={orgInfo.state}
                    onChange={(e) => updateOrgInfo("state", e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="org-postal-code">Почтовый индекс</Label>
                  <Input
                    id="org-postal-code"
                    value={orgInfo.postalCode}
                    onChange={(e) => updateOrgInfo("postalCode", e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="org-country">Страна</Label>
                  <Input
                    id="org-country"
                    value={orgInfo.country}
                    onChange={(e) => updateOrgInfo("country", e.target.value)}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="org-description">Описание</Label>
                <Textarea
                  id="org-description"
                  value={orgInfo.description}
                  onChange={(e) => updateOrgInfo("description", e.target.value)}
                  rows={4}
                />
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-end">
            <Button onClick={saveChanges}>Сохранить изменения</Button>
          </div>
        </TabsContent>

        <TabsContent value="departments" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Отделы</CardTitle>
                  <CardDescription>Управление отделами организации</CardDescription>
                </div>
                <Dialog open={showNewDepartmentDialog} onOpenChange={setShowNewDepartmentDialog}>
                  <DialogTrigger asChild>
                    <Button>
                      <Plus className="mr-2 h-4 w-4" />
                      Добавить отдел
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Создание нового отдела</DialogTitle>
                      <DialogDescription>Заполните информацию о новом отделе</DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4 py-4">
                      <div>
                        <Label htmlFor="dept-name">Название отдела</Label>
                        <Input
                          id="dept-name"
                          value={newDepartment.name}
                          onChange={(e) => setNewDepartment({ ...newDepartment, name: e.target.value })}
                          placeholder="Например: Финансы"
                        />
                      </div>
                      <div>
                        <Label htmlFor="dept-manager">Руководитель</Label>
                        <Select
                          value={newDepartment.manager}
                          onValueChange={(value) => setNewDepartment({ ...newDepartment, manager: value })}
                        >
                          <SelectTrigger id="dept-manager">
                            <SelectValue placeholder="Выберите руководителя" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Не назначен">Не назначен</SelectItem>
                            {employees.map((emp) => (
                              <SelectItem key={emp.id} value={emp.name}>
                                {emp.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="dept-location">Локация</Label>
                        <Select
                          value={newDepartment.location}
                          onValueChange={(value) => setNewDepartment({ ...newDepartment, location: value })}
                        >
                          <SelectTrigger id="dept-location">
                            <SelectValue placeholder="Выберите локацию" />
                          </SelectTrigger>
                          <SelectContent>
                            {locations.map((loc) => (
                              <SelectItem key={loc.id} value={loc.name}>
                                {loc.name}
                              </SelectItem>
                            ))}
                            <SelectItem value="Удаленно">Удаленно</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <DialogFooter>
                      <Button variant="outline" onClick={() => setShowNewDepartmentDialog(false)}>
                        Отмена
                      </Button>
                      <Button onClick={addDepartment}>Создать отдел</Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Название</TableHead>
                    <TableHead>Руководитель</TableHead>
                    <TableHead>Сотрудники</TableHead>
                    <TableHead>Локация</TableHead>
                    <TableHead>Действия</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {departments.map((dept) => (
                    <TableRow key={dept.id}>
                      <TableCell className="font-medium">{dept.name}</TableCell>
                      <TableCell>{dept.manager}</TableCell>
                      <TableCell>{dept.employees}</TableCell>
                      <TableCell>{dept.location}</TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="destructive" size="sm" onClick={() => deleteDepartment(dept.id)}>
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="employees" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Сотрудники</CardTitle>
                  <CardDescription>Управление сотрудниками организации</CardDescription>
                </div>
                <Dialog open={showNewEmployeeDialog} onOpenChange={setShowNewEmployeeDialog}>
                  <DialogTrigger asChild>
                    <Button>
                      <Plus className="mr-2 h-4 w-4" />
                      Добавить сотрудника
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Добавление нового сотрудника</DialogTitle>
                      <DialogDescription>Заполните информацию о новом сотруднике</DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4 py-4">
                      <div>
                        <Label htmlFor="emp-name">ФИО</Label>
                        <Input
                          id="emp-name"
                          value={newEmployee.name}
                          onChange={(e) => setNewEmployee({ ...newEmployee, name: e.target.value })}
                          placeholder="Иванов Иван Иванович"
                        />
                      </div>
                      <div>
                        <Label htmlFor="emp-position">Должность</Label>
                        <Input
                          id="emp-position"
                          value={newEmployee.position}
                          onChange={(e) => setNewEmployee({ ...newEmployee, position: e.target.value })}
                          placeholder="Например: Менеджер проектов"
                        />
                      </div>
                      <div>
                        <Label htmlFor="emp-department">Отдел</Label>
                        <Select
                          value={newEmployee.department}
                          onValueChange={(value) => setNewEmployee({ ...newEmployee, department: value })}
                        >
                          <SelectTrigger id="emp-department">
                            <SelectValue placeholder="Выберите отдел" />
                          </SelectTrigger>
                          <SelectContent>
                            {departments.map((dept) => (
                              <SelectItem key={dept.id} value={dept.name}>
                                {dept.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="emp-email">Email</Label>
                        <Input
                          id="emp-email"
                          type="email"
                          value={newEmployee.email}
                          onChange={(e) => setNewEmployee({ ...newEmployee, email: e.target.value })}
                          placeholder="<EMAIL>"
                        />
                      </div>
                      <div>
                        <Label htmlFor="emp-phone">Телефон</Label>
                        <Input
                          id="emp-phone"
                          value={newEmployee.phone}
                          onChange={(e) => setNewEmployee({ ...newEmployee, phone: e.target.value })}
                          placeholder="+7 (*************"
                        />
                      </div>
                    </div>
                    <DialogFooter>
                      <Button variant="outline" onClick={() => setShowNewEmployeeDialog(false)}>
                        Отмена
                      </Button>
                      <Button onClick={addEmployee}>Добавить сотрудника</Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Сотрудник</TableHead>
                    <TableHead>Должность</TableHead>
                    <TableHead>Отдел</TableHead>
                    <TableHead>Контакты</TableHead>
                    <TableHead>Статус</TableHead>
                    <TableHead>Дата приема</TableHead>
                    <TableHead>Действия</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {employees.map((emp) => (
                    <TableRow key={emp.id}>
                      <TableCell className="font-medium">{emp.name}</TableCell>
                      <TableCell>{emp.position}</TableCell>
                      <TableCell>{emp.department}</TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span className="text-xs">{emp.email}</span>
                          <span className="text-xs text-muted-foreground">{emp.phone}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Select
                          value={emp.status}
                          onValueChange={(value) => updateEmployeeStatus(emp.id, value as Employee["status"])}
                        >
                          <SelectTrigger className="w-32">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="active">
                              <div className="flex items-center">
                                <div className="w-2 h-2 bg-green-500 rounded-full mr-2" />
                                Активен
                              </div>
                            </SelectItem>
                            <SelectItem value="vacation">
                              <div className="flex items-center">
                                <div className="w-2 h-2 bg-blue-500 rounded-full mr-2" />
                                Отпуск
                              </div>
                            </SelectItem>
                            <SelectItem value="sick">
                              <div className="flex items-center">
                                <div className="w-2 h-2 bg-yellow-500 rounded-full mr-2" />
                                Больничный
                              </div>
                            </SelectItem>
                            <SelectItem value="remote">
                              <div className="flex items-center">
                                <div className="w-2 h-2 bg-purple-500 rounded-full mr-2" />
                                Удаленно
                              </div>
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </TableCell>
                      <TableCell>{emp.joinDate}</TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm">
                            <User className="h-4 w-4" />
                          </Button>
                          <Button variant="destructive" size="sm" onClick={() => deleteEmployee(emp.id)}>
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="locations" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Локации</CardTitle>
                  <CardDescription>Управление офисами и локациями организации</CardDescription>
                </div>
                <Dialog open={showNewLocationDialog} onOpenChange={setShowNewLocationDialog}>
                  <DialogTrigger asChild>
                    <Button>
                      <Plus className="mr-2 h-4 w-4" />
                      Добавить локацию
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Добавление новой локации</DialogTitle>
                      <DialogDescription>Заполните информацию о новой локации</DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4 py-4">
                      <div>
                        <Label htmlFor="loc-name">Название</Label>
                        <Input
                          id="loc-name"
                          value={newLocation.name}
                          onChange={(e) => setNewLocation({ ...newLocation, name: e.target.value })}
                          placeholder="Например: Южный офис"
                        />
                      </div>
                      <div>
                        <Label htmlFor="loc-address">Адрес</Label>
                        <Input
                          id="loc-address"
                          value={newLocation.address}
                          onChange={(e) => setNewLocation({ ...newLocation, address: e.target.value })}
                          placeholder="ул. Примерная, 123"
                        />
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="loc-city">Город</Label>
                          <Input
                            id="loc-city"
                            value={newLocation.city}
                            onChange={(e) => setNewLocation({ ...newLocation, city: e.target.value })}
                            placeholder="Москва"
                          />
                        </div>
                        <div>
                          <Label htmlFor="loc-country">Страна</Label>
                          <Input
                            id="loc-country"
                            value={newLocation.country}
                            onChange={(e) => setNewLocation({ ...newLocation, country: e.target.value })}
                            placeholder="Россия"
                          />
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="loc-headquarters"
                          checked={newLocation.isHeadquarters}
                          onCheckedChange={(checked) => setNewLocation({ ...newLocation, isHeadquarters: checked })}
                        />
                        <Label htmlFor="loc-headquarters">Штаб-квартира</Label>
                      </div>
                    </div>
                    <DialogFooter>
                      <Button variant="outline" onClick={() => setShowNewLocationDialog(false)}>
                        Отмена
                      </Button>
                      <Button onClick={addLocation}>Добавить локацию</Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {locations.map((loc) => (
                  <Card key={loc.id} className={loc.isHeadquarters ? "border-primary" : ""}>
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle className="text-lg">{loc.name}</CardTitle>
                          {loc.isHeadquarters && (
                            <div className="inline-block bg-primary/10 text-primary text-xs font-medium px-2 py-0.5 rounded mt-1">
                              Штаб-квартира
                            </div>
                          )}
                        </div>
                        <div className="flex gap-2">
                          {!loc.isHeadquarters && (
                            <Button variant="outline" size="sm" onClick={() => setHeadquarters(loc.id)}>
                              <Building2 className="h-4 w-4" />
                            </Button>
                          )}
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => deleteLocation(loc.id)}
                            disabled={loc.isHeadquarters}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="flex items-start">
                          <MapPin className="h-4 w-4 mt-1 mr-2 text-muted-foreground" />
                          <div>
                            <p className="text-sm">{loc.address}</p>
                            <p className="text-sm">
                              {loc.city}, {loc.country}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center">
                          <Users className="h-4 w-4 mr-2 text-muted-foreground" />
                          <p className="text-sm">{loc.employees} сотрудников</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="structure" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Организационная структура</CardTitle>
                  <CardDescription>Визуальное представление структуры организации</CardDescription>
                </div>
                <div className="flex gap-2">
                  <Select defaultValue="hierarchical" onValueChange={(value) => setChartLayout(value as any)}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Тип диаграммы" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="hierarchical">Иерархическая</SelectItem>
                      <SelectItem value="orgchart">Органиграмма</SelectItem>
                      <SelectItem value="network">Сетевая</SelectItem>
                    </SelectContent>
                  </Select>
                  <ExportChart chartRef={chartRef} fileName={`${orgInfo.name}-structure`} />
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="h-[600px] border rounded-md bg-background" ref={chartRef}>
                <OrganizationChart
                  departments={departments}
                  employees={employees}
                  orgInfo={orgInfo}
                  layout={chartLayout}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
