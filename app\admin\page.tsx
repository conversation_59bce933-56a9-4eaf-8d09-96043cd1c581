import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import {
  FileText,
  Star,
  DollarSign,
  Users,
  TrendingUp,
  TrendingDown,
  Activity,
  BarChart3,
  Settings,
  Plus,
  Eye,
  Edit,
  MessageSquare,
  CreditCard,
  Globe,
  Zap,
  Clock,
  CheckCircle,
  AlertCircle,
  ArrowUpRight,
  ArrowDownRight
} from "lucide-react"

export default function AdminDashboard() {
  return (
    <div className="space-y-6">
      {/* Заголовок */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Панель управления</h1>
          <p className="text-muted-foreground mt-2">
            Добро пожаловать в админ-панель. Управляйте контентом и настройками сайта
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Eye className="h-4 w-4 mr-2" />
            Просмотр сайта
          </Button>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Быстрое действие
          </Button>
        </div>
      </div>

      {/* Статистика */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="relative overflow-hidden">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Всего посетителей</CardTitle>
            <div className="p-2 bg-blue-100 rounded-lg">
              <Users className="h-4 w-4 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">12,345</div>
            <div className="flex items-center gap-1 mt-1">
              <TrendingUp className="h-3 w-3 text-green-500" />
              <span className="text-xs text-green-600 font-medium">+20.1%</span>
              <span className="text-xs text-muted-foreground">с прошлого месяца</span>
            </div>
            <Progress value={75} className="mt-3 h-1" />
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Конверсия</CardTitle>
            <div className="p-2 bg-green-100 rounded-lg">
              <BarChart3 className="h-4 w-4 text-green-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">3.2%</div>
            <div className="flex items-center gap-1 mt-1">
              <TrendingUp className="h-3 w-3 text-green-500" />
              <span className="text-xs text-green-600 font-medium">+1.2%</span>
              <span className="text-xs text-muted-foreground">с прошлой недели</span>
            </div>
            <Progress value={32} className="mt-3 h-1" />
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Активные подписки</CardTitle>
            <div className="p-2 bg-yellow-100 rounded-lg">
              <DollarSign className="h-4 w-4 text-yellow-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">573</div>
            <div className="flex items-center gap-1 mt-1">
              <TrendingUp className="h-3 w-3 text-green-500" />
              <span className="text-xs text-green-600 font-medium">+12</span>
              <span className="text-xs text-muted-foreground">новых сегодня</span>
            </div>
            <Progress value={85} className="mt-3 h-1" />
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Средний рейтинг</CardTitle>
            <div className="p-2 bg-purple-100 rounded-lg">
              <Star className="h-4 w-4 text-purple-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">4.8</div>
            <div className="flex items-center gap-1 mt-1">
              <Star className="h-3 w-3 text-yellow-500 fill-current" />
              <span className="text-xs text-muted-foreground">На основе 234 отзывов</span>
            </div>
            <Progress value={96} className="mt-3 h-1" />
          </CardContent>
        </Card>
      </div>

      {/* Основной контент */}
      <div className="grid gap-6 lg:grid-cols-3">
        {/* Быстрые действия */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5" />
                  Быстрые действия
                </CardTitle>
                <CardDescription>
                  Часто используемые функции для управления сайтом
                </CardDescription>
              </div>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-2" />
                Настроить
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid gap-3 md:grid-cols-2">
              <a href="/admin/hero" className="group">
                <div className="flex items-center gap-3 p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                  <div className="p-2 bg-blue-100 rounded-lg group-hover:bg-blue-200 transition-colors">
                    <Edit className="h-4 w-4 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <p className="font-medium">Главный заголовок</p>
                    <p className="text-sm text-muted-foreground">Редактировать hero секцию</p>
                  </div>
                  <ArrowUpRight className="h-4 w-4 text-muted-foreground group-hover:text-foreground" />
                </div>
              </a>

              <a href="/admin/features" className="group">
                <div className="flex items-center gap-3 p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                  <div className="p-2 bg-green-100 rounded-lg group-hover:bg-green-200 transition-colors">
                    <Plus className="h-4 w-4 text-green-600" />
                  </div>
                  <div className="flex-1">
                    <p className="font-medium">Функции</p>
                    <p className="text-sm text-muted-foreground">Добавить новую функцию</p>
                  </div>
                  <ArrowUpRight className="h-4 w-4 text-muted-foreground group-hover:text-foreground" />
                </div>
              </a>

              <a href="/admin/testimonials" className="group">
                <div className="flex items-center gap-3 p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                  <div className="p-2 bg-yellow-100 rounded-lg group-hover:bg-yellow-200 transition-colors">
                    <MessageSquare className="h-4 w-4 text-yellow-600" />
                  </div>
                  <div className="flex-1">
                    <p className="font-medium">Отзывы</p>
                    <p className="text-sm text-muted-foreground">Модерировать отзывы</p>
                  </div>
                  <ArrowUpRight className="h-4 w-4 text-muted-foreground group-hover:text-foreground" />
                </div>
              </a>

              <a href="/admin/pricing" className="group">
                <div className="flex items-center gap-3 p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                  <div className="p-2 bg-purple-100 rounded-lg group-hover:bg-purple-200 transition-colors">
                    <CreditCard className="h-4 w-4 text-purple-600" />
                  </div>
                  <div className="flex-1">
                    <p className="font-medium">Цены</p>
                    <p className="text-sm text-muted-foreground">Обновить тарифы</p>
                  </div>
                  <ArrowUpRight className="h-4 w-4 text-muted-foreground group-hover:text-foreground" />
                </div>
              </a>

              <a href="/admin/settings/integration" className="group">
                <div className="flex items-center gap-3 p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                  <div className="p-2 bg-orange-100 rounded-lg group-hover:bg-orange-200 transition-colors">
                    <Globe className="h-4 w-4 text-orange-600" />
                  </div>
                  <div className="flex-1">
                    <p className="font-medium">Интеграции</p>
                    <p className="text-sm text-muted-foreground">Управление интеграциями</p>
                  </div>
                  <ArrowUpRight className="h-4 w-4 text-muted-foreground group-hover:text-foreground" />
                </div>
              </a>

              <a href="/admin/organization" className="group">
                <div className="flex items-center gap-3 p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                  <div className="p-2 bg-indigo-100 rounded-lg group-hover:bg-indigo-200 transition-colors">
                    <Users className="h-4 w-4 text-indigo-600" />
                  </div>
                  <div className="flex-1">
                    <p className="font-medium">Организация</p>
                    <p className="text-sm text-muted-foreground">Структура компании</p>
                  </div>
                  <ArrowUpRight className="h-4 w-4 text-muted-foreground group-hover:text-foreground" />
                </div>
              </a>
            </div>
          </CardContent>
        </Card>

        {/* Боковая панель */}
        <div className="space-y-6">
          {/* Последние изменения */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Последние изменения
              </CardTitle>
              <CardDescription>
                Недавние обновления контента
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-start gap-3">
                <div className="p-1 bg-green-100 rounded-full">
                  <CheckCircle className="h-3 w-3 text-green-600" />
                </div>
                <div className="flex-1 space-y-1">
                  <p className="text-sm font-medium">Hero заголовок обновлен</p>
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3 text-muted-foreground" />
                    <p className="text-xs text-muted-foreground">2 часа назад</p>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="flex items-start gap-3">
                <div className="p-1 bg-blue-100 rounded-full">
                  <CheckCircle className="h-3 w-3 text-blue-600" />
                </div>
                <div className="flex-1 space-y-1">
                  <p className="text-sm font-medium">Новый отзыв добавлен</p>
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3 text-muted-foreground" />
                    <p className="text-xs text-muted-foreground">5 часов назад</p>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="flex items-start gap-3">
                <div className="p-1 bg-yellow-100 rounded-full">
                  <CheckCircle className="h-3 w-3 text-yellow-600" />
                </div>
                <div className="flex-1 space-y-1">
                  <p className="text-sm font-medium">Цены обновлены</p>
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3 text-muted-foreground" />
                    <p className="text-xs text-muted-foreground">1 день назад</p>
                  </div>
                </div>
              </div>

              <Separator />

              <Button variant="outline" className="w-full" size="sm">
                Показать все изменения
              </Button>
            </CardContent>
          </Card>

          {/* Системная информация */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Система
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Статус сервера</span>
                  <Badge variant="success" className="text-xs">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Онлайн
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Использование CPU</span>
                  <span className="text-sm text-muted-foreground">23%</span>
                </div>
                <Progress value={23} className="h-1" />
              </div>

              <Separator />

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Использование памяти</span>
                  <span className="text-sm text-muted-foreground">67%</span>
                </div>
                <Progress value={67} className="h-1" />
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <span className="text-sm">Версия</span>
                <Badge variant="outline" className="text-xs">v2.1.0</Badge>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
