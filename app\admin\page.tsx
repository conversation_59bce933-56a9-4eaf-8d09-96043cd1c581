import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { FileText, Star, DollarSign, Users } from "lucide-react"

export default function AdminDashboard() {
  return (
    <div>
      <h1 className="text-3xl font-bold mb-8">Дашборд</h1>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Всего посетителей</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">12,345</div>
            <p className="text-xs text-muted-foreground">+20.1% с прошлого месяца</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Конверсия</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">3.2%</div>
            <p className="text-xs text-muted-foreground">+1.2% с прошлой недели</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Активные подписки</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">573</div>
            <p className="text-xs text-muted-foreground">+12 новых сегодня</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Средний рейтинг</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">4.8</div>
            <p className="text-xs text-muted-foreground">На основе 234 отзывов</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Быстрые действия</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <a href="/admin/hero" className="block p-3 rounded hover:bg-gray-100">
              Редактировать главный заголовок
            </a>
            <a href="/admin/features" className="block p-3 rounded hover:bg-gray-100">
              Добавить новую функцию
            </a>
            <a href="/admin/testimonials" className="block p-3 rounded hover:bg-gray-100">
              Модерировать отзывы
            </a>
            <a href="/admin/pricing" className="block p-3 rounded hover:bg-gray-100">
              Обновить цены
            </a>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Последние изменения</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="text-sm">
              <p className="font-medium">Hero заголовок обновлен</p>
              <p className="text-muted-foreground">2 часа назад</p>
            </div>
            <div className="text-sm">
              <p className="font-medium">Новый отзыв добавлен</p>
              <p className="text-muted-foreground">5 часов назад</p>
            </div>
            <div className="text-sm">
              <p className="font-medium">Цены обновлены</p>
              <p className="text-muted-foreground">1 день назад</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
