"use client"

import * as React from "react"
import { Check, ChevronsUpDown } from "lucide-react"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"

import * as Icons from "lucide-react"

// Список популярных социальных сетей и их иконок
const socialIcons = [
  { value: "Facebook", label: "Facebook", icon: "Facebook" },
  { value: "Twitter", label: "Twitter", icon: "Twitter" },
  { value: "Instagram", label: "Instagram", icon: "Instagram" },
  { value: "Linkedin", label: "LinkedIn", icon: "Linkedin" },
  { value: "Youtube", label: "YouTube", icon: "Youtube" },
  { value: "Gith<PERSON>", label: "GitH<PERSON>", icon: "Gith<PERSON>" },
  { value: "Dribbble", label: "Dribbble", icon: "Dribbble" },
  { value: "Twitch", label: "Twitch", icon: "Twitch" },
  { value: "Mail", label: "Email", icon: "Mail" },
  { value: "Phone", label: "Phone", icon: "Phone" },
  { value: "Globe", label: "Website", icon: "Globe" },
  { value: "Rss", label: "RSS", icon: "Rss" },
  { value: "MessageCircle", label: "Messenger", icon: "MessageCircle" },
  { value: "Send", label: "Telegram", icon: "Send" },
  { value: "Slack", label: "Slack", icon: "Slack" },
  { value: "Discord", label: "Discord", icon: "MessageSquare" },
  { value: "Pinterest", label: "Pinterest", icon: "Pin" },
  { value: "Tiktok", label: "TikTok", icon: "Music2" },
  { value: "Snapchat", label: "Snapchat", icon: "Ghost" },
  { value: "Whatsapp", label: "WhatsApp", icon: "MessageSquare" },
]

interface IconSelectProps {
  value: string
  onChange: (value: string) => void
}

export function IconSelect({ value, onChange }: IconSelectProps) {
  const [open, setOpen] = React.useState(false)

  // Динамически получаем иконку из Lucide
  const IconComponent = value ? (Icons as any)[value] || Icons.Globe : Icons.Globe

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" role="combobox" aria-expanded={open} className="w-full justify-between">
          <div className="flex items-center">
            <IconComponent className="mr-2 h-4 w-4" />
            <span>{value ? socialIcons.find((icon) => icon.value === value)?.label || value : "Выберите иконку"}</span>
          </div>
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[200px] p-0">
        <Command>
          <CommandInput placeholder="Поиск иконки..." />
          <CommandList>
            <CommandEmpty>Иконка не найдена.</CommandEmpty>
            <CommandGroup className="max-h-[300px] overflow-y-auto">
              {socialIcons.map((icon) => {
                const IconComp = (Icons as any)[icon.icon]
                return (
                  <CommandItem
                    key={icon.value}
                    value={icon.value}
                    onSelect={() => {
                      onChange(icon.value)
                      setOpen(false)
                    }}
                  >
                    <div className="flex items-center">
                      <IconComp className="mr-2 h-4 w-4" />
                      {icon.label}
                    </div>
                    <Check className={cn("ml-auto h-4 w-4", value === icon.value ? "opacity-100" : "opacity-0")} />
                  </CommandItem>
                )
              })}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
