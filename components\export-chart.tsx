"use client"

import type React from "react"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Download, Printer, Share2 } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import html2canvas from "html2canvas"
import { jsPDF } from "jspdf"

interface ExportChartProps {
  chartRef: React.RefObject<HTMLDivElement>
  fileName?: string
}

export function ExportChart({ chartRef, fileName = "organization-chart" }: ExportChartProps) {
  const [isExporting, setIsExporting] = useState(false)

  // Функция для экспорта в PNG
  const exportToPng = async () => {
    if (!chartRef.current) return

    setIsExporting(true)
    try {
      const canvas = await html2canvas(chartRef.current, {
        backgroundColor: "#ffffff",
        scale: 2, // Увеличиваем масштаб для лучшего качества
      })

      const dataUrl = canvas.toDataURL("image/png")
      const link = document.createElement("a")
      link.download = `${fileName}.png`
      link.href = dataUrl
      link.click()
    } catch (error) {
      console.error("Ошибка при экспорте в PNG:", error)
    } finally {
      setIsExporting(false)
    }
  }

  // Функция для экспорта в PDF
  const exportToPdf = async () => {
    if (!chartRef.current) return

    setIsExporting(true)
    try {
      const canvas = await html2canvas(chartRef.current, {
        backgroundColor: "#ffffff",
        scale: 2,
      })

      const imgData = canvas.toDataURL("image/png")
      const pdf = new jsPDF({
        orientation: "landscape",
        unit: "mm",
      })

      // Вычисляем размеры для сохранения пропорций
      const imgWidth = 280
      const imgHeight = (canvas.height * imgWidth) / canvas.width

      pdf.addImage(imgData, "PNG", 10, 10, imgWidth, imgHeight)
      pdf.save(`${fileName}.pdf`)
    } catch (error) {
      console.error("Ошибка при экспорте в PDF:", error)
    } finally {
      setIsExporting(false)
    }
  }

  // Функция для печати
  const printChart = async () => {
    if (!chartRef.current) return

    setIsExporting(true)
    try {
      const canvas = await html2canvas(chartRef.current, {
        backgroundColor: "#ffffff",
        scale: 2,
      })

      const dataUrl = canvas.toDataURL("image/png")

      const printWindow = window.open("", "_blank")
      if (!printWindow) return

      printWindow.document.write(`
        <html>
          <head>
            <title>Организационная структура</title>
            <style>
              body { margin: 0; display: flex; justify-content: center; }
              img { max-width: 100%; height: auto; }
            </style>
          </head>
          <body>
            <img src="${dataUrl}" alt="Организационная структура" />
            <script>
              window.onload = function() {
                setTimeout(function() {
                  window.print();
                  window.close();
                }, 500);
              };
            </script>
          </body>
        </html>
      `)
      printWindow.document.close()
    } catch (error) {
      console.error("Ошибка при печати:", error)
    } finally {
      setIsExporting(false)
    }
  }

  return (
    <div className="flex gap-2">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm" disabled={isExporting}>
            <Download className="h-4 w-4 mr-2" />
            Экспорт
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuItem onClick={exportToPng}>Экспорт в PNG</DropdownMenuItem>
          <DropdownMenuItem onClick={exportToPdf}>Экспорт в PDF</DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <Button variant="outline" size="sm" onClick={printChart} disabled={isExporting}>
        <Printer className="h-4 w-4 mr-2" />
        Печать
      </Button>

      <Button variant="outline" size="sm" disabled={isExporting}>
        <Share2 className="h-4 w-4 mr-2" />
        Поделиться
      </Button>
    </div>
  )
}
