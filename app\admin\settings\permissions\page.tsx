"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Shield,
  Users,
  Key,
  Lock,
  UserPlus,
  UserMinus,
  Settings,
  FileText,
  Eye,
  Edit,
  Trash2,
  Plus,
  AlertCircle,
} from "lucide-react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/dialog"

interface Role {
  id: string
  name: string
  description: string
  permissions: string[]
  isSystem: boolean
}

interface User {
  id: string
  name: string
  email: string
  role: string
  status: "active" | "inactive" | "suspended"
  lastLogin: string
}

interface Permission {
  id: string
  name: string
  description: string
  category: string
  icon: any
}

const availablePermissions: Permission[] = [
  // Управление контентом
  {
    id: "content.view",
    name: "Просмотр контента",
    description: "Просмотр всего контента",
    category: "Контент",
    icon: Eye,
  },
  {
    id: "content.create",
    name: "Создание контента",
    description: "Создание нового контента",
    category: "Контент",
    icon: Plus,
  },
  {
    id: "content.edit",
    name: "Редактирование контента",
    description: "Редактирование существующего контента",
    category: "Контент",
    icon: Edit,
  },
  {
    id: "content.delete",
    name: "Удаление контента",
    description: "Удаление контента",
    category: "Контент",
    icon: Trash2,
  },

  // Управление пользователями
  {
    id: "users.view",
    name: "Просмотр пользователей",
    description: "Просмотр списка пользователей",
    category: "Пользователи",
    icon: Users,
  },
  {
    id: "users.create",
    name: "Создание пользователей",
    description: "Создание новых пользователей",
    category: "Пользователи",
    icon: UserPlus,
  },
  {
    id: "users.edit",
    name: "Редактирование пользователей",
    description: "Редактирование данных пользователей",
    category: "Пользователи",
    icon: Edit,
  },
  {
    id: "users.delete",
    name: "Удаление пользователей",
    description: "Удаление пользователей",
    category: "Пользователи",
    icon: UserMinus,
  },

  // Настройки
  {
    id: "settings.view",
    name: "Просмотр настроек",
    description: "Просмотр настроек системы",
    category: "Настройки",
    icon: Settings,
  },
  {
    id: "settings.edit",
    name: "Изменение настроек",
    description: "Изменение настроек системы",
    category: "Настройки",
    icon: Settings,
  },

  // Аналитика
  {
    id: "analytics.view",
    name: "Просмотр аналитики",
    description: "Просмотр аналитических данных",
    category: "Аналитика",
    icon: FileText,
  },
  {
    id: "analytics.export",
    name: "Экспорт аналитики",
    description: "Экспорт аналитических данных",
    category: "Аналитика",
    icon: FileText,
  },
]

export default function PermissionsPage() {
  const { toast } = useToast()

  const [roles, setRoles] = useState<Role[]>([
    {
      id: "admin",
      name: "Администратор",
      description: "Полный доступ ко всем функциям системы",
      permissions: availablePermissions.map((p) => p.id),
      isSystem: true,
    },
    {
      id: "editor",
      name: "Редактор",
      description: "Управление контентом и просмотр аналитики",
      permissions: ["content.view", "content.create", "content.edit", "analytics.view"],
      isSystem: false,
    },
    {
      id: "viewer",
      name: "Наблюдатель",
      description: "Только просмотр контента и аналитики",
      permissions: ["content.view", "analytics.view"],
      isSystem: false,
    },
  ])

  const [users, setUsers] = useState<User[]>([
    {
      id: "1",
      name: "Иван Иванов",
      email: "<EMAIL>",
      role: "admin",
      status: "active",
      lastLogin: "2024-01-15 14:30",
    },
    {
      id: "2",
      name: "Мария Петрова",
      email: "<EMAIL>",
      role: "editor",
      status: "active",
      lastLogin: "2024-01-15 10:15",
    },
    {
      id: "3",
      name: "Алексей Сидоров",
      email: "<EMAIL>",
      role: "viewer",
      status: "inactive",
      lastLogin: "2024-01-10 16:45",
    },
  ])

  const [newRole, setNewRole] = useState({
    name: "",
    description: "",
    permissions: [] as string[],
  })

  const [editingRole, setEditingRole] = useState<Role | null>(null)
  const [showNewRoleDialog, setShowNewRoleDialog] = useState(false)
  const [showEditRoleDialog, setShowEditRoleDialog] = useState(false)

  const [securitySettings, setSecuritySettings] = useState({
    enforcePasswordPolicy: true,
    minPasswordLength: 8,
    requireUppercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
    passwordExpireDays: 90,
    maxLoginAttempts: 5,
    lockoutDuration: 30,
    enableTwoFactor: false,
    sessionTimeout: 60,
    enableAuditLog: true,
    enableIpWhitelist: false,
    ipWhitelist: "",
  })

  const updateSecuritySetting = (key: keyof typeof securitySettings, value: any) => {
    setSecuritySettings({
      ...securitySettings,
      [key]: value,
    })
  }

  const createRole = () => {
    if (newRole.name && newRole.description) {
      const role: Role = {
        id: `role-${Date.now()}`,
        name: newRole.name,
        description: newRole.description,
        permissions: newRole.permissions,
        isSystem: false,
      }
      setRoles([...roles, role])
      setNewRole({ name: "", description: "", permissions: [] })
      setShowNewRoleDialog(false)
      toast({
        title: "Роль создана",
        description: `Роль "${role.name}" успешно создана`,
      })
    }
  }

  const updateRole = () => {
    if (editingRole) {
      setRoles(roles.map((role) => (role.id === editingRole.id ? editingRole : role)))
      setShowEditRoleDialog(false)
      setEditingRole(null)
      toast({
        title: "Роль обновлена",
        description: `Роль "${editingRole.name}" успешно обновлена`,
      })
    }
  }

  const deleteRole = (roleId: string) => {
    const role = roles.find((r) => r.id === roleId)
    if (role && !role.isSystem) {
      setRoles(roles.filter((r) => r.id !== roleId))
      // Переназначаем пользователей с удаленной роли на роль "viewer"
      setUsers(users.map((user) => (user.role === roleId ? { ...user, role: "viewer" } : user)))
      toast({
        title: "Роль удалена",
        description: `Роль "${role.name}" успешно удалена`,
      })
    }
  }

  const updateUserRole = (userId: string, newRole: string) => {
    setUsers(users.map((user) => (user.id === userId ? { ...user, role: newRole } : user)))
    toast({
      title: "Роль пользователя обновлена",
      description: "Изменения сохранены",
    })
  }

  const updateUserStatus = (userId: string, newStatus: User["status"]) => {
    setUsers(users.map((user) => (user.id === userId ? { ...user, status: newStatus } : user)))
    toast({
      title: "Статус пользователя обновлен",
      description: "Изменения сохранены",
    })
  }

  const togglePermission = (permissions: string[], permissionId: string): string[] => {
    if (permissions.includes(permissionId)) {
      return permissions.filter((p) => p !== permissionId)
    } else {
      return [...permissions, permissionId]
    }
  }

  const saveSecuritySettings = () => {
    toast({
      title: "Настройки безопасности сохранены",
      description: "Все изменения успешно применены",
    })
  }

  const getPermissionsByCategory = () => {
    const grouped: { [key: string]: Permission[] } = {}
    availablePermissions.forEach((permission) => {
      if (!grouped[permission.category]) {
        grouped[permission.category] = []
      }
      grouped[permission.category].push(permission)
    })
    return grouped
  }

  const permissionsByCategory = getPermissionsByCategory()

  return (
    <div>
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold">Управление разрешениями</h1>
          <p className="text-muted-foreground mt-2">Настройка ролей, разрешений и политик безопасности</p>
        </div>
        <Shield className="h-8 w-8 text-muted-foreground" />
      </div>

      <Tabs defaultValue="roles" className="space-y-4">
        <TabsList>
          <TabsTrigger value="roles">Роли и разрешения</TabsTrigger>
          <TabsTrigger value="users">Пользователи</TabsTrigger>
          <TabsTrigger value="security">Политики безопасности</TabsTrigger>
        </TabsList>

        <TabsContent value="roles" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Роли системы</CardTitle>
                  <CardDescription>Управление ролями и их разрешениями</CardDescription>
                </div>
                <Dialog open={showNewRoleDialog} onOpenChange={setShowNewRoleDialog}>
                  <DialogTrigger asChild>
                    <Button>
                      <Plus className="mr-2 h-4 w-4" />
                      Создать роль
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-2xl">
                    <DialogHeader>
                      <DialogTitle>Создание новой роли</DialogTitle>
                      <DialogDescription>
                        Укажите название, описание и выберите разрешения для новой роли
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4 py-4">
                      <div>
                        <Label htmlFor="role-name">Название роли</Label>
                        <Input
                          id="role-name"
                          value={newRole.name}
                          onChange={(e) => setNewRole({ ...newRole, name: e.target.value })}
                          placeholder="Например: Модератор"
                        />
                      </div>
                      <div>
                        <Label htmlFor="role-description">Описание</Label>
                        <Input
                          id="role-description"
                          value={newRole.description}
                          onChange={(e) => setNewRole({ ...newRole, description: e.target.value })}
                          placeholder="Краткое описание роли"
                        />
                      </div>
                      <div>
                        <Label>Разрешения</Label>
                        <div className="mt-2 space-y-4 max-h-64 overflow-y-auto">
                          {Object.entries(permissionsByCategory).map(([category, permissions]) => (
                            <div key={category}>
                              <h4 className="font-medium mb-2">{category}</h4>
                              <div className="space-y-2 pl-4">
                                {permissions.map((permission) => (
                                  <div key={permission.id} className="flex items-center space-x-2">
                                    <Switch
                                      id={`new-${permission.id}`}
                                      checked={newRole.permissions.includes(permission.id)}
                                      onCheckedChange={() => {
                                        setNewRole({
                                          ...newRole,
                                          permissions: togglePermission(newRole.permissions, permission.id),
                                        })
                                      }}
                                    />
                                    <Label
                                      htmlFor={`new-${permission.id}`}
                                      className="text-sm font-normal cursor-pointer"
                                    >
                                      {permission.name}
                                      <span className="text-muted-foreground ml-2 text-xs">
                                        {permission.description}
                                      </span>
                                    </Label>
                                  </div>
                                ))}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                    <DialogFooter>
                      <Button variant="outline" onClick={() => setShowNewRoleDialog(false)}>
                        Отмена
                      </Button>
                      <Button onClick={createRole}>Создать роль</Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {roles.map((role) => (
                  <Card key={role.id}>
                    <CardHeader>
                      <div className="flex justify-between items-start">
                        <div>
                          <div className="flex items-center gap-2">
                            <CardTitle className="text-lg">{role.name}</CardTitle>
                            {role.isSystem && <Badge variant="secondary">Системная</Badge>}
                          </div>
                          <CardDescription>{role.description}</CardDescription>
                        </div>
                        <div className="flex gap-2">
                          {!role.isSystem && (
                            <>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  setEditingRole(role)
                                  setShowEditRoleDialog(true)
                                }}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button variant="destructive" size="sm" onClick={() => deleteRole(role.id)}>
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </>
                          )}
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="flex flex-wrap gap-2">
                        {role.permissions.map((permissionId) => {
                          const permission = availablePermissions.find((p) => p.id === permissionId)
                          if (!permission) return null
                          const Icon = permission.icon
                          return (
                            <Badge key={permissionId} variant="outline">
                              <Icon className="mr-1 h-3 w-3" />
                              {permission.name}
                            </Badge>
                          )
                        })}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Диалог редактирования роли */}
          <Dialog open={showEditRoleDialog} onOpenChange={setShowEditRoleDialog}>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Редактирование роли</DialogTitle>
                <DialogDescription>Измените параметры и разрешения роли</DialogDescription>
              </DialogHeader>
              {editingRole && (
                <div className="space-y-4 py-4">
                  <div>
                    <Label htmlFor="edit-role-name">Название роли</Label>
                    <Input
                      id="edit-role-name"
                      value={editingRole.name}
                      onChange={(e) => setEditingRole({ ...editingRole, name: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="edit-role-description">Описание</Label>
                    <Input
                      id="edit-role-description"
                      value={editingRole.description}
                      onChange={(e) => setEditingRole({ ...editingRole, description: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label>Разрешения</Label>
                    <div className="mt-2 space-y-4 max-h-64 overflow-y-auto">
                      {Object.entries(permissionsByCategory).map(([category, permissions]) => (
                        <div key={category}>
                          <h4 className="font-medium mb-2">{category}</h4>
                          <div className="space-y-2 pl-4">
                            {permissions.map((permission) => (
                              <div key={permission.id} className="flex items-center space-x-2">
                                <Switch
                                  id={`edit-${permission.id}`}
                                  checked={editingRole.permissions.includes(permission.id)}
                                  onCheckedChange={() => {
                                    setEditingRole({
                                      ...editingRole,
                                      permissions: togglePermission(editingRole.permissions, permission.id),
                                    })
                                  }}
                                />
                                <Label htmlFor={`edit-${permission.id}`} className="text-sm font-normal cursor-pointer">
                                  {permission.name}
                                  <span className="text-muted-foreground ml-2 text-xs">{permission.description}</span>
                                </Label>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
              <DialogFooter>
                <Button variant="outline" onClick={() => setShowEditRoleDialog(false)}>
                  Отмена
                </Button>
                <Button onClick={updateRole}>Сохранить изменения</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </TabsContent>

        <TabsContent value="users" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Управление пользователями</CardTitle>
              <CardDescription>Назначение ролей и управление доступом пользователей</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Пользователь</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Роль</TableHead>
                    <TableHead>Статус</TableHead>
                    <TableHead>Последний вход</TableHead>
                    <TableHead>Действия</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {users.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell className="font-medium">{user.name}</TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>
                        <Select value={user.role} onValueChange={(value) => updateUserRole(user.id, value)}>
                          <SelectTrigger className="w-32">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {roles.map((role) => (
                              <SelectItem key={role.id} value={role.id}>
                                {role.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </TableCell>
                      <TableCell>
                        <Select
                          value={user.status}
                          onValueChange={(value) => updateUserStatus(user.id, value as User["status"])}
                        >
                          <SelectTrigger className="w-32">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="active">
                              <div className="flex items-center">
                                <div className="w-2 h-2 bg-green-500 rounded-full mr-2" />
                                Активен
                              </div>
                            </SelectItem>
                            <SelectItem value="inactive">
                              <div className="flex items-center">
                                <div className="w-2 h-2 bg-gray-500 rounded-full mr-2" />
                                Неактивен
                              </div>
                            </SelectItem>
                            <SelectItem value="suspended">
                              <div className="flex items-center">
                                <div className="w-2 h-2 bg-red-500 rounded-full mr-2" />
                                Заблокирован
                              </div>
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </TableCell>
                      <TableCell className="text-muted-foreground">{user.lastLogin}</TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm">
                            <Key className="h-4 w-4" />
                          </Button>
                          <Button variant="outline" size="sm">
                            <Lock className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Политика паролей</CardTitle>
              <CardDescription>Настройка требований к паролям пользователей</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="enforce-password"
                  checked={securitySettings.enforcePasswordPolicy}
                  onCheckedChange={(checked) => updateSecuritySetting("enforcePasswordPolicy", checked)}
                />
                <Label htmlFor="enforce-password">Применять политику паролей</Label>
              </div>

              {securitySettings.enforcePasswordPolicy && (
                <div className="space-y-4 pl-6">
                  <div>
                    <Label htmlFor="min-length">Минимальная длина пароля</Label>
                    <Input
                      id="min-length"
                      type="number"
                      value={securitySettings.minPasswordLength}
                      onChange={(e) => updateSecuritySetting("minPasswordLength", Number.parseInt(e.target.value))}
                      className="w-24"
                    />
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="require-uppercase"
                        checked={securitySettings.requireUppercase}
                        onCheckedChange={(checked) => updateSecuritySetting("requireUppercase", checked)}
                      />
                      <Label htmlFor="require-uppercase">Требовать заглавные буквы</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        id="require-numbers"
                        checked={securitySettings.requireNumbers}
                        onCheckedChange={(checked) => updateSecuritySetting("requireNumbers", checked)}
                      />
                      <Label htmlFor="require-numbers">Требовать цифры</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        id="require-special"
                        checked={securitySettings.requireSpecialChars}
                        onCheckedChange={(checked) => updateSecuritySetting("requireSpecialChars", checked)}
                      />
                      <Label htmlFor="require-special">Требовать специальные символы</Label>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="password-expire">Срок действия пароля (дней)</Label>
                    <Input
                      id="password-expire"
                      type="number"
                      value={securitySettings.passwordExpireDays}
                      onChange={(e) => updateSecuritySetting("passwordExpireDays", Number.parseInt(e.target.value))}
                      className="w-24"
                    />
                    <p className="text-sm text-muted-foreground mt-1">
                      Пользователи должны будут менять пароль каждые {securitySettings.passwordExpireDays} дней
                    </p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Безопасность входа</CardTitle>
              <CardDescription>Настройка параметров входа и блокировки</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="max-attempts">Максимальное количество попыток входа</Label>
                <Input
                  id="max-attempts"
                  type="number"
                  value={securitySettings.maxLoginAttempts}
                  onChange={(e) => updateSecuritySetting("maxLoginAttempts", Number.parseInt(e.target.value))}
                  className="w-24"
                />
              </div>

              <div>
                <Label htmlFor="lockout-duration">Длительность блокировки (минут)</Label>
                <Input
                  id="lockout-duration"
                  type="number"
                  value={securitySettings.lockoutDuration}
                  onChange={(e) => updateSecuritySetting("lockoutDuration", Number.parseInt(e.target.value))}
                  className="w-24"
                />
                <p className="text-sm text-muted-foreground mt-1">
                  Время блокировки после превышения лимита попыток входа
                </p>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="two-factor"
                  checked={securitySettings.enableTwoFactor}
                  onCheckedChange={(checked) => updateSecuritySetting("enableTwoFactor", checked)}
                />
                <Label htmlFor="two-factor">Требовать двухфакторную аутентификацию</Label>
              </div>

              <div>
                <Label htmlFor="session-timeout">Тайм-аут сессии (минут)</Label>
                <Input
                  id="session-timeout"
                  type="number"
                  value={securitySettings.sessionTimeout}
                  onChange={(e) => updateSecuritySetting("sessionTimeout", Number.parseInt(e.target.value))}
                  className="w-24"
                />
                <p className="text-sm text-muted-foreground mt-1">
                  Автоматический выход из системы после периода неактивности
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Дополнительные настройки</CardTitle>
              <CardDescription>Расширенные параметры безопасности</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="audit-log"
                  checked={securitySettings.enableAuditLog}
                  onCheckedChange={(checked) => updateSecuritySetting("enableAuditLog", checked)}
                />
                <Label htmlFor="audit-log">Вести журнал аудита</Label>
              </div>

              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="ip-whitelist"
                    checked={securitySettings.enableIpWhitelist}
                    onCheckedChange={(checked) => updateSecuritySetting("enableIpWhitelist", checked)}
                  />
                  <Label htmlFor="ip-whitelist">Включить белый список IP-адресов</Label>
                </div>

                {securitySettings.enableIpWhitelist && (
                  <div className="pl-6">
                    <Label htmlFor="ip-list">Разрешенные IP-адреса</Label>
                    <Input
                      id="ip-list"
                      value={securitySettings.ipWhitelist}
                      onChange={(e) => updateSecuritySetting("ipWhitelist", e.target.value)}
                      placeholder="***********, 10.0.0.0/24"
                    />
                    <p className="text-sm text-muted-foreground mt-1">Введите IP-адреса или подсети через запятую</p>
                  </div>
                )}
              </div>

              <div className="pt-4 border-t">
                <div className="flex items-center gap-2 text-amber-600 mb-4">
                  <AlertCircle className="h-5 w-5" />
                  <p className="text-sm font-medium">Изменение этих настроек может повлиять на безопасность системы</p>
                </div>
                <Button onClick={saveSecuritySettings}>Сохранить настройки безопасности</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
