"use client"

import type * as React from "react"
import { useRef, useState } from "react"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { Upload, X } from "lucide-react"

interface FileUploadProps extends React.InputHTMLAttributes<HTMLInputElement> {
  onFileChange: (file: File | null) => void
  previewUrl?: string
  className?: string
}

export function FileUpload({ onFileChange, previewUrl, className, ...props }: FileUploadProps) {
  const inputRef = useRef<HTMLInputElement>(null)
  const [preview, setPreview] = useState<string | null>(previewUrl || null)

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null
    if (file) {
      const reader = new FileReader()
      reader.onload = () => {
        setPreview(reader.result as string)
      }
      reader.readAsDataURL(file)
      onFileChange(file)
    }
  }

  const handleRemove = () => {
    setPreview(null)
    if (inputRef.current) {
      inputRef.current.value = ""
    }
    onFileChange(null)
  }

  return (
    <div className={cn("space-y-4", className)}>
      {preview ? (
        <div className="relative inline-block">
          <Image
            src={preview || "/placeholder.svg"}
            alt="Preview"
            width={128}
            height={128}
            className="max-h-32 w-auto rounded-md"
          />
          <button
            type="button"
            onClick={handleRemove}
            className="absolute -right-2 -top-2 rounded-full bg-destructive p-1 text-destructive-foreground"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      ) : (
        <div
          onClick={() => inputRef.current?.click()}
          className="flex h-32 w-full cursor-pointer flex-col items-center justify-center rounded-md border border-dashed border-muted-foreground/25 p-4 transition-colors hover:border-muted-foreground/50"
        >
          <Upload className="mb-2 h-6 w-6 text-muted-foreground" />
          <p className="text-sm text-muted-foreground">Нажмите для загрузки или перетащите файл</p>
          <p className="text-xs text-muted-foreground">SVG, PNG, JPG (макс. 2MB)</p>
        </div>
      )}
      <input
        type="file"
        ref={inputRef}
        onChange={handleFileChange}
        className="hidden"
        accept="image/png,image/jpeg,image/svg+xml"
        {...props}
      />
      {!preview && (
        <Button type="button" variant="outline" size="sm" onClick={() => inputRef.current?.click()}>
          Выбрать файл
        </Button>
      )}
    </div>
  )
}
