"use client"

import type * as React from "react"
import { Check } from "lucide-react"
import { cn } from "@/lib/utils"

interface PresetOption {
  id: string
  name: string
  description: string
  preview: React.ReactNode
}

interface PresetSelectorProps {
  options: PresetOption[]
  value: string
  onChange: (value: string) => void
  className?: string
}

export function PresetSelector({ options, value, onChange, className }: PresetSelectorProps) {
  return (
    <div className={cn("grid gap-4", className)}>
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3">
        {options.map((option) => (
          <div
            key={option.id}
            className={cn(
              "relative flex cursor-pointer flex-col items-center rounded-md border bg-background p-4 shadow-sm transition-all hover:border-primary",
              value === option.id && "border-2 border-primary",
            )}
            onClick={() => onChange(option.id)}
          >
            {value === option.id && (
              <div className="absolute right-2 top-2 h-5 w-5 rounded-full bg-primary text-primary-foreground">
                <Check className="h-5 w-5" />
              </div>
            )}
            <div className="mb-2 flex h-16 w-full items-center justify-center">{option.preview}</div>
            <div className="text-center">
              <h3 className="font-medium">{option.name}</h3>
              <p className="text-xs text-muted-foreground">{option.description}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
