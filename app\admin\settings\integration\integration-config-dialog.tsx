"use client"

import React, { useState, useEffect } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Checkbox } from "@/components/ui/checkbox"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import {
  ExternalLink,
  Eye,
  EyeOff,
  Copy,
  Check,
  Loader2,
  AlertCircle,
  Shield,
  Zap
} from "lucide-react"

interface Integration {
  id: string
  name: string
  description: string
  category: string
  icon: React.ElementType
  status: 'connected' | 'disconnected' | 'error' | 'configuring'
  setupComplexity: 'easy' | 'medium' | 'advanced'
  features: string[]
  documentation?: string
  config?: {
    apiKey?: string
    apiSecret?: string
    environment?: string
    autoSync?: boolean
    notifications?: boolean
    syncFrequency?: string
    webhookUrl?: string
    webhookSecret?: string
    selectedEvents?: string[]
    spaceUrl?: string
    clientId?: string
    [key: string]: any
  }
}

interface IntegrationConfigDialogProps {
  integration: Integration | null
  isOpen: boolean
  onClose: () => void
  onConnect: (integration: Integration) => Promise<void>
  isConnecting: boolean
}

export function IntegrationConfigDialog({
  integration,
  isOpen,
  onClose,
  onConnect,
  isConnecting
}: IntegrationConfigDialogProps) {
  const [showApiKey, setShowApiKey] = useState(false)
  const [showApiSecret, setShowApiSecret] = useState(false)
  const [isTesting, setIsTesting] = useState(false)
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null)

  // Инициализация формы для каждой интеграции отдельно
  const getInitialFormData = () => ({
    apiKey: integration?.config?.apiKey || '',
    apiSecret: integration?.config?.apiSecret || '',
    environment: integration?.config?.environment || 'production',
    autoSync: integration?.config?.autoSync ?? true,
    notifications: integration?.config?.notifications ?? true,
    syncFrequency: integration?.config?.syncFrequency || '15',
    webhookUrl: integration?.config?.webhookUrl || '',
    webhookSecret: integration?.config?.webhookSecret || '',
    selectedEvents: integration?.config?.selectedEvents || ['payment.succeeded', 'customer.created']
  })

  const [formData, setFormData] = useState(getInitialFormData())

  // Сброс формы при смене интеграции
  useEffect(() => {
    if (integration) {
      setFormData(getInitialFormData())
      setTestResult(null)
      setShowApiKey(false)
      setShowApiSecret(false)
    }
  }, [integration?.id])

  const handleSubmit = async () => {
    if (integration) {
      // Сохраняем конфигурацию в интеграцию
      const updatedIntegration = {
        ...integration,
        config: formData
      }
      await onConnect(updatedIntegration)
    }
  }

  const handleTestConnection = async () => {
    if (!formData.apiKey) {
      setTestResult({
        success: false,
        message: 'Пожалуйста, введите API ключ'
      })
      return
    }

    setIsTesting(true)
    setTestResult(null)

    try {
      // Симуляция тестирования подключения
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Простая проверка валидности ключа (в реальности здесь был бы API вызов)
      const isValidKey = formData.apiKey.length > 10

      if (isValidKey) {
        setTestResult({
          success: true,
          message: `Подключение к ${integration?.name} успешно установлено!`
        })
      } else {
        setTestResult({
          success: false,
          message: 'Неверный API ключ. Проверьте правильность введенных данных.'
        })
      }
    } catch (error) {
      setTestResult({
        success: false,
        message: 'Ошибка при тестировании подключения. Попробуйте позже.'
      })
    } finally {
      setIsTesting(false)
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    // Можно добавить toast уведомление
  }

  if (!integration) return null

  const Icon = integration.icon

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Icon className="h-5 w-5" />
            Настройка {integration.name}
          </DialogTitle>
          <DialogDescription>
            Настройте параметры подключения для интеграции с {integration.name}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Информация об интеграции */}
          <div className="p-4 bg-muted rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-medium">Информация об интеграции</h4>
              {integration.documentation && (
                <Button variant="outline" size="sm" asChild>
                  <a href={integration.documentation} target="_blank" rel="noopener noreferrer">
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Документация
                  </a>
                </Button>
              )}
            </div>
            <p className="text-sm text-muted-foreground mb-3">
              {integration.description}
            </p>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <Label>Категория:</Label>
                <p className="capitalize">{integration.category}</p>
              </div>
              <div>
                <Label>Сложность настройки:</Label>
                <Badge variant={
                  integration.setupComplexity === 'easy' ? 'success' :
                  integration.setupComplexity === 'medium' ? 'secondary' : 'destructive'
                }>
                  {integration.setupComplexity === 'easy' ? 'Легко' :
                   integration.setupComplexity === 'medium' ? 'Средне' : 'Сложно'}
                </Badge>
              </div>
            </div>
          </div>

          {/* Настройки подключения */}
          <Tabs defaultValue="credentials" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="credentials">Учетные данные</TabsTrigger>
              <TabsTrigger value="settings">Настройки</TabsTrigger>
              <TabsTrigger value="webhooks">Webhooks</TabsTrigger>
            </TabsList>

            <TabsContent value="credentials" className="space-y-4">
              <div className="space-y-4">
                {/* Результат тестирования */}
                {testResult && (
                  <div className={`p-3 rounded-lg border ${
                    testResult.success
                      ? 'bg-green-50 border-green-200 text-green-800'
                      : 'bg-red-50 border-red-200 text-red-800'
                  }`}>
                    <div className="flex items-center gap-2">
                      {testResult.success ? (
                        <Check className="h-4 w-4" />
                      ) : (
                        <AlertCircle className="h-4 w-4" />
                      )}
                      <span className="text-sm font-medium">{testResult.message}</span>
                    </div>
                  </div>
                )}

                {/* API ключ */}
                <div>
                  <Label htmlFor="api-key">
                    API ключ *
                    {integration.id === 'stripe' && (
                      <span className="text-xs text-muted-foreground ml-1">(sk_live_... или sk_test_...)</span>
                    )}
                    {integration.id === 'twilio' && (
                      <span className="text-xs text-muted-foreground ml-1">(Account SID)</span>
                    )}
                    {integration.id === 'google-analytics' && (
                      <span className="text-xs text-muted-foreground ml-1">(Measurement ID)</span>
                    )}
                  </Label>
                  <div className="flex gap-2">
                    <Input
                      id="api-key"
                      type={showApiKey ? "text" : "password"}
                      placeholder={
                        integration.id === 'stripe' ? "sk_live_..." :
                        integration.id === 'twilio' ? "AC..." :
                        integration.id === 'google-analytics' ? "G-..." :
                        integration.id === 'signalwire' ? "Ваш Project ID" :
                        "Введите ваш API ключ"
                      }
                      value={formData.apiKey}
                      onChange={(e) => setFormData(prev => ({ ...prev, apiKey: e.target.value }))}
                      required
                    />
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => setShowApiKey(!showApiKey)}
                    >
                      {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => copyToClipboard(formData.apiKey)}
                      disabled={!formData.apiKey}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* API секрет */}
                <div>
                  <Label htmlFor="api-secret">
                    {integration.id === 'stripe' ? 'Webhook секрет' :
                     integration.id === 'twilio' ? 'Auth Token' :
                     integration.id === 'signalwire' ? 'API Token' :
                     'API секрет'}
                    {(integration.id === 'stripe' || integration.id === 'twilio' || integration.id === 'signalwire') && ' *'}
                  </Label>
                  <div className="flex gap-2">
                    <Input
                      id="api-secret"
                      type={showApiSecret ? "text" : "password"}
                      placeholder={
                        integration.id === 'stripe' ? "whsec_..." :
                        integration.id === 'twilio' ? "Ваш Auth Token" :
                        integration.id === 'signalwire' ? "Ваш API Token" :
                        "Введите ваш API секрет"
                      }
                      value={formData.apiSecret}
                      onChange={(e) => setFormData(prev => ({ ...prev, apiSecret: e.target.value }))}
                      required={integration.id === 'stripe' || integration.id === 'twilio' || integration.id === 'signalwire'}
                    />
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => setShowApiSecret(!showApiSecret)}
                    >
                      {showApiSecret ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>

                {/* Дополнительные поля для SignalWire */}
                {integration.id === 'signalwire' && (
                  <div>
                    <Label htmlFor="space-url">Space URL *</Label>
                    <Input
                      id="space-url"
                      placeholder="example.signalwire.com"
                      value={formData.spaceUrl || ''}
                      onChange={(e) => setFormData(prev => ({ ...prev, spaceUrl: e.target.value }))}
                      required
                    />
                  </div>
                )}

                {/* Дополнительные поля для PayPal */}
                {integration.id === 'paypal' && (
                  <div>
                    <Label htmlFor="client-id">Client ID *</Label>
                    <Input
                      id="client-id"
                      placeholder="Ваш PayPal Client ID"
                      value={formData.clientId || ''}
                      onChange={(e) => setFormData(prev => ({ ...prev, clientId: e.target.value }))}
                      required
                    />
                  </div>
                )}

                <div>
                  <Label htmlFor="environment">Среда</Label>
                  <Select
                    value={formData.environment}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, environment: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="production">
                        <div className="flex items-center gap-2">
                          <Shield className="h-4 w-4 text-green-500" />
                          Продакшн
                        </div>
                      </SelectItem>
                      <SelectItem value="sandbox">
                        <div className="flex items-center gap-2">
                          <Zap className="h-4 w-4 text-yellow-500" />
                          Песочница
                        </div>
                      </SelectItem>
                      <SelectItem value="development">
                        <div className="flex items-center gap-2">
                          <AlertCircle className="h-4 w-4 text-blue-500" />
                          Разработка
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="settings" className="space-y-4">
              <div className="space-y-4">
                {/* Автоматическая синхронизация */}
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <Label className="font-medium">Автоматическая синхронизация</Label>
                      <Badge variant="outline" className="text-xs">
                        {formData.autoSync ? 'Включено' : 'Отключено'}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      Автоматически синхронизировать данные каждые {formData.syncFrequency} минут
                    </p>
                  </div>
                  <Switch
                    checked={formData.autoSync}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, autoSync: checked }))}
                  />
                </div>

                {/* Частота синхронизации */}
                {formData.autoSync && (
                  <div className="p-4 border rounded-lg bg-muted/50">
                    <Label htmlFor="sync-frequency" className="font-medium">Частота синхронизации</Label>
                    <p className="text-sm text-muted-foreground mb-3">
                      Выберите, как часто система должна синхронизировать данные с {integration.name}
                    </p>
                    <Select
                      value={formData.syncFrequency}
                      onValueChange={(value) => setFormData(prev => ({ ...prev, syncFrequency: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1">Каждую минуту</SelectItem>
                        <SelectItem value="5">Каждые 5 минут</SelectItem>
                        <SelectItem value="15">Каждые 15 минут</SelectItem>
                        <SelectItem value="30">Каждые 30 минут</SelectItem>
                        <SelectItem value="60">Каждый час</SelectItem>
                        <SelectItem value="360">Каждые 6 часов</SelectItem>
                        <SelectItem value="1440">Раз в день</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}

                {/* Уведомления */}
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <Label className="font-medium">Уведомления об ошибках</Label>
                      <Badge variant="outline" className="text-xs">
                        {formData.notifications ? 'Включено' : 'Отключено'}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      Получать email уведомления при возникновении ошибок синхронизации
                    </p>
                  </div>
                  <Switch
                    checked={formData.notifications}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, notifications: checked }))}
                  />
                </div>

                {/* Специфичные настройки для разных интеграций */}
                {integration.id === 'signalwire' && (
                  <div className="p-4 border rounded-lg">
                    <Label className="font-medium">Настройки SignalWire</Label>
                    <div className="space-y-3 mt-3">
                      <div className="flex items-center justify-between">
                        <div>
                          <Label className="text-sm">Запись звонков</Label>
                          <p className="text-xs text-muted-foreground">Автоматически записывать все звонки</p>
                        </div>
                        <Switch
                          checked={formData.recordCalls || false}
                          onCheckedChange={(checked) => setFormData(prev => ({ ...prev, recordCalls: checked }))}
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <div>
                          <Label className="text-sm">AI транскрипция</Label>
                          <p className="text-xs text-muted-foreground">Преобразовывать речь в текст</p>
                        </div>
                        <Switch
                          checked={formData.aiTranscription || false}
                          onCheckedChange={(checked) => setFormData(prev => ({ ...prev, aiTranscription: checked }))}
                        />
                      </div>
                    </div>
                  </div>
                )}

                {integration.id === 'stripe' && (
                  <div className="p-4 border rounded-lg">
                    <Label className="font-medium">Настройки Stripe</Label>
                    <div className="space-y-3 mt-3">
                      <div>
                        <Label htmlFor="currency">Валюта по умолчанию</Label>
                        <Select
                          value={formData.defaultCurrency || 'usd'}
                          onValueChange={(value) => setFormData(prev => ({ ...prev, defaultCurrency: value }))}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="usd">USD - Доллар США</SelectItem>
                            <SelectItem value="eur">EUR - Евро</SelectItem>
                            <SelectItem value="rub">RUB - Российский рубль</SelectItem>
                            <SelectItem value="gbp">GBP - Британский фунт</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="flex items-center justify-between">
                        <div>
                          <Label className="text-sm">Автоматические возвраты</Label>
                          <p className="text-xs text-muted-foreground">Разрешить автоматические возвраты</p>
                        </div>
                        <Switch
                          checked={formData.autoRefunds || false}
                          onCheckedChange={(checked) => setFormData(prev => ({ ...prev, autoRefunds: checked }))}
                        />
                      </div>
                    </div>
                  </div>
                )}

                {integration.id === 'google-analytics' && (
                  <div className="p-4 border rounded-lg">
                    <Label className="font-medium">Настройки Google Analytics</Label>
                    <div className="space-y-3 mt-3">
                      <div className="flex items-center justify-between">
                        <div>
                          <Label className="text-sm">Enhanced E-commerce</Label>
                          <p className="text-xs text-muted-foreground">Отслеживание покупок и конверсий</p>
                        </div>
                        <Switch
                          checked={formData.enhancedEcommerce || false}
                          onCheckedChange={(checked) => setFormData(prev => ({ ...prev, enhancedEcommerce: checked }))}
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <div>
                          <Label className="text-sm">Демографические данные</Label>
                          <p className="text-xs text-muted-foreground">Сбор данных о возрасте и поле</p>
                        </div>
                        <Switch
                          checked={formData.demographics || false}
                          onCheckedChange={(checked) => setFormData(prev => ({ ...prev, demographics: checked }))}
                        />
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="webhooks" className="space-y-4">
              <div className="space-y-4">
                {/* Информация о webhooks */}
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-start gap-2">
                    <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-blue-900">Что такое Webhooks?</h4>
                      <p className="text-sm text-blue-700 mt-1">
                        Webhooks позволяют {integration.name} отправлять уведомления о событиях в реальном времени на ваш сервер.
                        Это обеспечивает мгновенную синхронизацию данных.
                      </p>
                    </div>
                  </div>
                </div>

                {/* Webhook URL */}
                <div className="p-4 border rounded-lg">
                  <Label htmlFor="webhook-url" className="font-medium">Webhook URL</Label>
                  <p className="text-sm text-muted-foreground mb-3">
                    URL эндпоинта, на который {integration.name} будет отправлять уведомления о событиях
                  </p>
                  <div className="flex gap-2">
                    <Input
                      id="webhook-url"
                      placeholder={`https://your-domain.com/webhooks/${integration.id}`}
                      value={formData.webhookUrl}
                      onChange={(e) => setFormData(prev => ({ ...prev, webhookUrl: e.target.value }))}
                    />
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => copyToClipboard(formData.webhookUrl)}
                      disabled={!formData.webhookUrl}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                  {formData.webhookUrl && (
                    <div className="mt-2 p-2 bg-green-50 border border-green-200 rounded text-sm text-green-700">
                      ✓ URL настроен корректно
                    </div>
                  )}
                </div>

                {/* Webhook секрет */}
                <div className="p-4 border rounded-lg">
                  <Label htmlFor="webhook-secret" className="font-medium">Webhook секрет</Label>
                  <p className="text-sm text-muted-foreground mb-3">
                    Секретный ключ для проверки подлинности webhook запросов от {integration.name}
                  </p>
                  <div className="flex gap-2">
                    <Input
                      id="webhook-secret"
                      type="password"
                      placeholder="Введите секретный ключ"
                      value={formData.webhookSecret}
                      onChange={(e) => setFormData(prev => ({ ...prev, webhookSecret: e.target.value }))}
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const secret = 'whsec_' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)
                        setFormData(prev => ({ ...prev, webhookSecret: secret }))
                      }}
                    >
                      Генерировать
                    </Button>
                  </div>
                </div>

                {/* События для отслеживания */}
                <div className="p-4 border rounded-lg">
                  <Label className="font-medium">События для отслеживания</Label>
                  <p className="text-sm text-muted-foreground mb-3">
                    Выберите события, о которых {integration.name} должен уведомлять ваш сервер
                  </p>

                  {/* Специфичные события для каждой интеграции */}
                  <div className="space-y-3">
                    {integration.id === 'stripe' && (
                      <>
                        <div className="font-medium text-sm text-muted-foreground">Платежные события:</div>
                        {['payment_intent.succeeded', 'payment_intent.payment_failed', 'charge.dispute.created'].map((event) => (
                          <div key={event} className="flex items-center space-x-2 p-2 hover:bg-muted/50 rounded">
                            <Checkbox
                              id={event}
                              checked={formData.selectedEvents.includes(event)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setFormData(prev => ({
                                    ...prev,
                                    selectedEvents: [...prev.selectedEvents, event]
                                  }))
                                } else {
                                  setFormData(prev => ({
                                    ...prev,
                                    selectedEvents: prev.selectedEvents.filter(e => e !== event)
                                  }))
                                }
                              }}
                            />
                            <div className="flex-1">
                              <Label htmlFor={event} className="text-sm font-medium">{event}</Label>
                              <p className="text-xs text-muted-foreground">
                                {event === 'payment_intent.succeeded' && 'Платеж успешно завершен'}
                                {event === 'payment_intent.payment_failed' && 'Платеж не удался'}
                                {event === 'charge.dispute.created' && 'Создан спор по платежу'}
                              </p>
                            </div>
                          </div>
                        ))}

                        <div className="font-medium text-sm text-muted-foreground mt-4">Клиентские события:</div>
                        {['customer.created', 'customer.updated', 'customer.deleted'].map((event) => (
                          <div key={event} className="flex items-center space-x-2 p-2 hover:bg-muted/50 rounded">
                            <Checkbox
                              id={event}
                              checked={formData.selectedEvents.includes(event)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setFormData(prev => ({
                                    ...prev,
                                    selectedEvents: [...prev.selectedEvents, event]
                                  }))
                                } else {
                                  setFormData(prev => ({
                                    ...prev,
                                    selectedEvents: prev.selectedEvents.filter(e => e !== event)
                                  }))
                                }
                              }}
                            />
                            <div className="flex-1">
                              <Label htmlFor={event} className="text-sm font-medium">{event}</Label>
                              <p className="text-xs text-muted-foreground">
                                {event === 'customer.created' && 'Новый клиент создан'}
                                {event === 'customer.updated' && 'Данные клиента обновлены'}
                                {event === 'customer.deleted' && 'Клиент удален'}
                              </p>
                            </div>
                          </div>
                        ))}
                      </>
                    )}

                    {integration.id === 'signalwire' && (
                      <>
                        <div className="font-medium text-sm text-muted-foreground">Звонки и сообщения:</div>
                        {['call.initiated', 'call.answered', 'call.completed', 'message.received'].map((event) => (
                          <div key={event} className="flex items-center space-x-2 p-2 hover:bg-muted/50 rounded">
                            <Checkbox
                              id={event}
                              checked={formData.selectedEvents.includes(event)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setFormData(prev => ({
                                    ...prev,
                                    selectedEvents: [...prev.selectedEvents, event]
                                  }))
                                } else {
                                  setFormData(prev => ({
                                    ...prev,
                                    selectedEvents: prev.selectedEvents.filter(e => e !== event)
                                  }))
                                }
                              }}
                            />
                            <div className="flex-1">
                              <Label htmlFor={event} className="text-sm font-medium">{event}</Label>
                              <p className="text-xs text-muted-foreground">
                                {event === 'call.initiated' && 'Звонок инициирован'}
                                {event === 'call.answered' && 'Звонок принят'}
                                {event === 'call.completed' && 'Звонок завершен'}
                                {event === 'message.received' && 'Получено сообщение'}
                              </p>
                            </div>
                          </div>
                        ))}
                      </>
                    )}

                    {integration.id === 'twilio' && (
                      <>
                        <div className="font-medium text-sm text-muted-foreground">SMS и звонки:</div>
                        {['message.sent', 'message.delivered', 'message.failed', 'call.completed'].map((event) => (
                          <div key={event} className="flex items-center space-x-2 p-2 hover:bg-muted/50 rounded">
                            <Checkbox
                              id={event}
                              checked={formData.selectedEvents.includes(event)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setFormData(prev => ({
                                    ...prev,
                                    selectedEvents: [...prev.selectedEvents, event]
                                  }))
                                } else {
                                  setFormData(prev => ({
                                    ...prev,
                                    selectedEvents: prev.selectedEvents.filter(e => e !== event)
                                  }))
                                }
                              }}
                            />
                            <div className="flex-1">
                              <Label htmlFor={event} className="text-sm font-medium">{event}</Label>
                              <p className="text-xs text-muted-foreground">
                                {event === 'message.sent' && 'SMS отправлено'}
                                {event === 'message.delivered' && 'SMS доставлено'}
                                {event === 'message.failed' && 'SMS не доставлено'}
                                {event === 'call.completed' && 'Звонок завершен'}
                              </p>
                            </div>
                          </div>
                        ))}
                      </>
                    )}

                    {/* Общие события для других интеграций */}
                    {!['stripe', 'signalwire', 'twilio'].includes(integration.id) && (
                      <>
                        <div className="font-medium text-sm text-muted-foreground">Основные события:</div>
                        {['data.updated', 'sync.completed', 'error.occurred'].map((event) => (
                          <div key={event} className="flex items-center space-x-2 p-2 hover:bg-muted/50 rounded">
                            <Checkbox
                              id={event}
                              checked={formData.selectedEvents.includes(event)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setFormData(prev => ({
                                    ...prev,
                                    selectedEvents: [...prev.selectedEvents, event]
                                  }))
                                } else {
                                  setFormData(prev => ({
                                    ...prev,
                                    selectedEvents: prev.selectedEvents.filter(e => e !== event)
                                  }))
                                }
                              }}
                            />
                            <div className="flex-1">
                              <Label htmlFor={event} className="text-sm font-medium">{event}</Label>
                              <p className="text-xs text-muted-foreground">
                                {event === 'data.updated' && 'Данные обновлены'}
                                {event === 'sync.completed' && 'Синхронизация завершена'}
                                {event === 'error.occurred' && 'Произошла ошибка'}
                              </p>
                            </div>
                          </div>
                        ))}
                      </>
                    )}
                  </div>

                  {/* Счетчик выбранных событий */}
                  <div className="mt-4 p-3 bg-muted rounded-lg">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">
                        Выбрано событий: {formData.selectedEvents.length}
                      </span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setFormData(prev => ({ ...prev, selectedEvents: [] }))}
                        disabled={formData.selectedEvents.length === 0}
                      >
                        Очистить все
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Тестирование webhook */}
                <div className="p-4 border rounded-lg">
                  <Label className="font-medium">Тестирование Webhook</Label>
                  <p className="text-sm text-muted-foreground mb-3">
                    Отправьте тестовое событие на ваш webhook URL для проверки настройки
                  </p>
                  <Button
                    variant="outline"
                    disabled={!formData.webhookUrl}
                    onClick={() => {
                      // Здесь можно добавить логику тестирования webhook
                      alert('Тестовое событие отправлено на ' + formData.webhookUrl)
                    }}
                  >
                    <Zap className="h-4 w-4 mr-2" />
                    Отправить тестовое событие
                  </Button>
                </div>
              </div>
            </TabsContent>
          </Tabs>

          {/* Кнопки действий */}
          <div className="flex justify-between">
            <Button variant="outline" onClick={onClose}>
              Отмена
            </Button>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={handleTestConnection}
                disabled={isTesting || !formData.apiKey}
              >
                {isTesting ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Zap className="h-4 w-4 mr-2" />
                )}
                Тест подключения
              </Button>
              <Button
                onClick={handleSubmit}
                disabled={isConnecting || !formData.apiKey}
              >
                {isConnecting ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Check className="h-4 w-4 mr-2" />
                )}
                {integration.status === 'connected' ? 'Сохранить' : 'Подключить'}
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
