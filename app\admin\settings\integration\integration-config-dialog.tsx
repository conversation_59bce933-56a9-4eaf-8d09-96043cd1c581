"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Checkbox } from "@/components/ui/checkbox"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { 
  ExternalLink, 
  Eye, 
  EyeOff, 
  Copy, 
  Check, 
  Loader2,
  AlertCircle,
  Shield,
  Zap
} from "lucide-react"

interface Integration {
  id: string
  name: string
  description: string
  category: string
  icon: React.ElementType
  status: 'connected' | 'disconnected' | 'error' | 'configuring'
  setupComplexity: 'easy' | 'medium' | 'advanced'
  features: string[]
  documentation?: string
}

interface IntegrationConfigDialogProps {
  integration: Integration | null
  isOpen: boolean
  onClose: () => void
  onConnect: (integration: Integration) => Promise<void>
  isConnecting: boolean
}

export function IntegrationConfigDialog({
  integration,
  isOpen,
  onClose,
  onConnect,
  isConnecting
}: IntegrationConfigDialogProps) {
  const [showApiKey, setShowApiKey] = useState(false)
  const [formData, setFormData] = useState({
    apiKey: '',
    apiSecret: '',
    environment: 'production',
    autoSync: true,
    notifications: true,
    syncFrequency: '15',
    webhookUrl: '',
    webhookSecret: '',
    selectedEvents: ['payment.succeeded', 'customer.created']
  })

  const handleSubmit = async () => {
    if (integration) {
      await onConnect(integration)
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  if (!integration) return null

  const Icon = integration.icon

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Icon className="h-5 w-5" />
            Настройка {integration.name}
          </DialogTitle>
          <DialogDescription>
            Настройте параметры подключения для интеграции с {integration.name}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Информация об интеграции */}
          <div className="p-4 bg-muted rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-medium">Информация об интеграции</h4>
              {integration.documentation && (
                <Button variant="outline" size="sm" asChild>
                  <a href={integration.documentation} target="_blank" rel="noopener noreferrer">
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Документация
                  </a>
                </Button>
              )}
            </div>
            <p className="text-sm text-muted-foreground mb-3">
              {integration.description}
            </p>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <Label>Категория:</Label>
                <p className="capitalize">{integration.category}</p>
              </div>
              <div>
                <Label>Сложность настройки:</Label>
                <Badge variant={
                  integration.setupComplexity === 'easy' ? 'success' :
                  integration.setupComplexity === 'medium' ? 'secondary' : 'destructive'
                }>
                  {integration.setupComplexity === 'easy' ? 'Легко' :
                   integration.setupComplexity === 'medium' ? 'Средне' : 'Сложно'}
                </Badge>
              </div>
            </div>
          </div>

          {/* Настройки подключения */}
          <Tabs defaultValue="credentials" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="credentials">Учетные данные</TabsTrigger>
              <TabsTrigger value="settings">Настройки</TabsTrigger>
              <TabsTrigger value="webhooks">Webhooks</TabsTrigger>
            </TabsList>

            <TabsContent value="credentials" className="space-y-4">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="api-key">API ключ</Label>
                  <div className="flex gap-2">
                    <Input
                      id="api-key"
                      type={showApiKey ? "text" : "password"}
                      placeholder="Введите ваш API ключ"
                      value={formData.apiKey}
                      onChange={(e) => setFormData(prev => ({ ...prev, apiKey: e.target.value }))}
                    />
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => setShowApiKey(!showApiKey)}
                    >
                      {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                    <Button 
                      variant="outline" 
                      size="icon"
                      onClick={() => copyToClipboard(formData.apiKey)}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div>
                  <Label htmlFor="api-secret">API секрет</Label>
                  <Input
                    id="api-secret"
                    type="password"
                    placeholder="Введите ваш API секрет"
                    value={formData.apiSecret}
                    onChange={(e) => setFormData(prev => ({ ...prev, apiSecret: e.target.value }))}
                  />
                </div>

                <div>
                  <Label htmlFor="environment">Среда</Label>
                  <Select 
                    value={formData.environment} 
                    onValueChange={(value) => setFormData(prev => ({ ...prev, environment: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="production">
                        <div className="flex items-center gap-2">
                          <Shield className="h-4 w-4 text-green-500" />
                          Продакшн
                        </div>
                      </SelectItem>
                      <SelectItem value="sandbox">
                        <div className="flex items-center gap-2">
                          <Zap className="h-4 w-4 text-yellow-500" />
                          Песочница
                        </div>
                      </SelectItem>
                      <SelectItem value="development">
                        <div className="flex items-center gap-2">
                          <AlertCircle className="h-4 w-4 text-blue-500" />
                          Разработка
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="settings" className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label>Автоматическая синхронизация</Label>
                    <p className="text-sm text-muted-foreground">
                      Автоматически синхронизировать данные каждые {formData.syncFrequency} минут
                    </p>
                  </div>
                  <Switch 
                    checked={formData.autoSync}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, autoSync: checked }))}
                  />
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div>
                    <Label>Уведомления об ошибках</Label>
                    <p className="text-sm text-muted-foreground">
                      Получать уведомления при возникновении ошибок
                    </p>
                  </div>
                  <Switch 
                    checked={formData.notifications}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, notifications: checked }))}
                  />
                </div>

                <Separator />

                <div>
                  <Label htmlFor="sync-frequency">Частота синхронизации</Label>
                  <Select 
                    value={formData.syncFrequency}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, syncFrequency: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="5">Каждые 5 минут</SelectItem>
                      <SelectItem value="15">Каждые 15 минут</SelectItem>
                      <SelectItem value="30">Каждые 30 минут</SelectItem>
                      <SelectItem value="60">Каждый час</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="webhooks" className="space-y-4">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="webhook-url">Webhook URL</Label>
                  <Input
                    id="webhook-url"
                    placeholder="https://your-domain.com/webhooks"
                    value={formData.webhookUrl}
                    onChange={(e) => setFormData(prev => ({ ...prev, webhookUrl: e.target.value }))}
                  />
                </div>

                <div>
                  <Label htmlFor="webhook-secret">Webhook секрет</Label>
                  <Input
                    id="webhook-secret"
                    type="password"
                    placeholder="Секретный ключ для проверки подписи"
                    value={formData.webhookSecret}
                    onChange={(e) => setFormData(prev => ({ ...prev, webhookSecret: e.target.value }))}
                  />
                </div>

                <div>
                  <Label>События для отслеживания</Label>
                  <div className="grid grid-cols-2 gap-2 mt-2">
                    {['payment.succeeded', 'payment.failed', 'customer.created', 'subscription.updated'].map((event) => (
                      <div key={event} className="flex items-center space-x-2">
                        <Checkbox 
                          id={event}
                          checked={formData.selectedEvents.includes(event)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setFormData(prev => ({ 
                                ...prev, 
                                selectedEvents: [...prev.selectedEvents, event] 
                              }))
                            } else {
                              setFormData(prev => ({ 
                                ...prev, 
                                selectedEvents: prev.selectedEvents.filter(e => e !== event) 
                              }))
                            }
                          }}
                        />
                        <Label htmlFor={event} className="text-sm">{event}</Label>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>

          {/* Кнопки действий */}
          <div className="flex justify-between">
            <Button variant="outline" onClick={onClose}>
              Отмена
            </Button>
            <div className="flex gap-2">
              <Button variant="outline">
                Тест подключения
              </Button>
              <Button onClick={handleSubmit} disabled={isConnecting}>
                {isConnecting ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Check className="h-4 w-4 mr-2" />
                )}
                {integration.status === 'connected' ? 'Сохранить' : 'Подключить'}
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
