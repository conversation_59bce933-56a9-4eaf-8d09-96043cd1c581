"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { Trash2, Plus, GripVertical, AlertCircle } from "lucide-react"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { FileUpload } from "@/components/ui/file-upload"
import { Switch } from "@/components/ui/switch"
import { ColorPicker } from "@/components/ui/color-picker"
import { IconSelect } from "@/components/ui/icon-select"
import { PresetSelector } from "@/components/ui/preset-selector"
import * as Icons from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"

interface FooterLink {
  id: string
  text: string
  url: string
}

interface FooterSection {
  id: string
  title: string
  links: FooterLink[]
}

interface FooterColors {
  background: string
  text: string
  headings: string
  links: string
  linksHover: string
  border: string
}

interface SocialLink {
  id: string
  icon: string
  url: string
  label: string
}

// Предустановленные наборы социальных сетей
const socialPresets = {
  basic: [
    { id: "facebook", icon: "Facebook", url: "https://facebook.com", label: "Facebook" },
    { id: "twitter", icon: "Twitter", url: "https://twitter.com", label: "Twitter" },
    { id: "instagram", icon: "Instagram", url: "https://instagram.com", label: "Instagram" },
  ],
  business: [
    { id: "facebook", icon: "Facebook", url: "https://facebook.com", label: "Facebook" },
    { id: "twitter", icon: "Twitter", url: "https://twitter.com", label: "Twitter" },
    { id: "linkedin", icon: "Linkedin", url: "https://linkedin.com", label: "LinkedIn" },
    { id: "youtube", icon: "Youtube", url: "https://youtube.com", label: "YouTube" },
  ],
  creative: [
    { id: "instagram", icon: "Instagram", url: "https://instagram.com", label: "Instagram" },
    { id: "dribbble", icon: "Dribbble", url: "https://dribbble.com", label: "Dribbble" },
    { id: "pinterest", icon: "Pin", url: "https://pinterest.com", label: "Pinterest" },
    { id: "behance", icon: "Figma", url: "https://behance.net", label: "Behance" },
  ],
  tech: [
    { id: "github", icon: "Github", url: "https://github.com", label: "GitHub" },
    { id: "twitter", icon: "Twitter", url: "https://twitter.com", label: "Twitter" },
    { id: "linkedin", icon: "Linkedin", url: "https://linkedin.com", label: "LinkedIn" },
    { id: "discord", icon: "MessageSquare", url: "https://discord.com", label: "Discord" },
  ],
  full: [
    { id: "facebook", icon: "Facebook", url: "https://facebook.com", label: "Facebook" },
    { id: "twitter", icon: "Twitter", url: "https://twitter.com", label: "Twitter" },
    { id: "instagram", icon: "Instagram", url: "https://instagram.com", label: "Instagram" },
    { id: "linkedin", icon: "Linkedin", url: "https://linkedin.com", label: "LinkedIn" },
    { id: "youtube", icon: "Youtube", url: "https://youtube.com", label: "YouTube" },
    { id: "pinterest", icon: "Pin", url: "https://pinterest.com", label: "Pinterest" },
    { id: "github", icon: "Github", url: "https://github.com", label: "GitHub" },
    { id: "tiktok", icon: "Music2", url: "https://tiktok.com", label: "TikTok" },
  ],
}

export default function AdminFooterPage() {
  const { toast } = useToast()
  const [footerSections, setFooterSections] = useState<FooterSection[]>([
    {
      id: "company",
      title: "Company",
      links: [
        { id: "about", text: "About", url: "#" },
        { id: "blog", text: "Blog", url: "#" },
        { id: "careers", text: "Careers", url: "#" },
        { id: "contact", text: "Contact", url: "#" },
      ],
    },
    {
      id: "product",
      title: "Product",
      links: [
        { id: "features", text: "Features", url: "#" },
        { id: "pricing", text: "Pricing", url: "#" },
        { id: "integrations", text: "Integrations", url: "#" },
        { id: "changelog", text: "Changelog", url: "#" },
      ],
    },
    {
      id: "legal",
      title: "Legal",
      links: [
        { id: "terms", text: "Terms", url: "#" },
        { id: "privacy", text: "Privacy", url: "#" },
        { id: "cookies", text: "Cookies", url: "#" },
        { id: "licenses", text: "Licenses", url: "#" },
      ],
    },
    {
      id: "resources",
      title: "Resources",
      links: [
        { id: "documentation", text: "Documentation", url: "#" },
        { id: "guides", text: "Guides", url: "#" },
        { id: "support", text: "Support", url: "#" },
        { id: "api", text: "API", url: "#" },
      ],
    },
  ])

  const [socialLinks, setSocialLinks] = useState<SocialLink[]>([
    { id: "facebook", icon: "Facebook", url: "https://facebook.com", label: "Facebook" },
    { id: "twitter", icon: "Twitter", url: "https://twitter.com", label: "Twitter" },
    { id: "instagram", icon: "Instagram", url: "https://instagram.com", label: "Instagram" },
    { id: "linkedin", icon: "Linkedin", url: "https://linkedin.com", label: "LinkedIn" },
  ])

  const [copyrightText, setCopyrightText] = useState("© 2023 ACME Inc. All rights reserved.")
  const [logoUrl, setLogoUrl] = useState<string | null>(null)
  const [showLogoInFooter, setShowLogoInFooter] = useState(true)
  const [logoFile, setLogoFile] = useState<File | null>(null)

  const [footerColors, setFooterColors] = useState<FooterColors>({
    background: "#f9fafb", // light gray
    text: "#6b7280", // gray-500
    headings: "#111827", // gray-900
    links: "#6b7280", // gray-500
    linksHover: "#111827", // gray-900
    border: "#e5e7eb", // gray-200
  })

  const [socialIconSize, setSocialIconSize] = useState(5)
  const [socialIconColor, setSocialIconColor] = useState("#6b7280")
  const [socialIconHoverColor, setSocialIconHoverColor] = useState("#111827")
  const [selectedPreset, setSelectedPreset] = useState("")
  const [presetApplyMode, setPresetApplyMode] = useState<"replace" | "merge">("replace")
  const [showPresetDialog, setShowPresetDialog] = useState(false)

  const updateSectionTitle = (sectionId: string, newTitle: string) => {
    setFooterSections(
      footerSections.map((section) => (section.id === sectionId ? { ...section, title: newTitle } : section)),
    )
  }

  const updateLinkText = (sectionId: string, linkId: string, newText: string) => {
    setFooterSections(
      footerSections.map((section) => {
        if (section.id === sectionId) {
          return {
            ...section,
            links: section.links.map((link) => (link.id === linkId ? { ...link, text: newText } : link)),
          }
        }
        return section
      }),
    )
  }

  const updateLinkUrl = (sectionId: string, linkId: string, newUrl: string) => {
    setFooterSections(
      footerSections.map((section) => {
        if (section.id === sectionId) {
          return {
            ...section,
            links: section.links.map((link) => (link.id === linkId ? { ...link, url: newUrl } : link)),
          }
        }
        return section
      }),
    )
  }

  const addLink = (sectionId: string) => {
    setFooterSections(
      footerSections.map((section) => {
        if (section.id === sectionId) {
          const newId = `new-link-${Date.now()}`
          return {
            ...section,
            links: [...section.links, { id: newId, text: "Новая ссылка", url: "#" }],
          }
        }
        return section
      }),
    )
  }

  const removeLink = (sectionId: string, linkId: string) => {
    setFooterSections(
      footerSections.map((section) => {
        if (section.id === sectionId) {
          return {
            ...section,
            links: section.links.filter((link) => link.id !== linkId),
          }
        }
        return section
      }),
    )
  }

  const handleLogoChange = (file: File | null) => {
    setLogoFile(file)
    if (file) {
      const reader = new FileReader()
      reader.onload = () => {
        setLogoUrl(reader.result as string)
      }
      reader.readAsDataURL(file)
    } else {
      setLogoUrl(null)
    }
  }

  const updateFooterColor = (key: keyof FooterColors, value: string) => {
    setFooterColors({
      ...footerColors,
      [key]: value,
    })
  }

  const resetColors = () => {
    setFooterColors({
      background: "#f9fafb", // light gray
      text: "#6b7280", // gray-500
      headings: "#111827", // gray-900
      links: "#6b7280", // gray-500
      linksHover: "#111827", // gray-900
      border: "#e5e7eb", // gray-200
    })
    toast({
      title: "Цвета сброшены",
      description: "Цвета футера сброшены до значений по умолчанию",
    })
  }

  const addSocialLink = () => {
    const newId = `social-${Date.now()}`
    setSocialLinks([...socialLinks, { id: newId, icon: "Globe", url: "https://", label: "Новая ссылка" }])
  }

  const updateSocialLink = (id: string, field: keyof SocialLink, value: string) => {
    setSocialLinks(socialLinks.map((link) => (link.id === id ? { ...link, [field]: value } : link)))
  }

  const removeSocialLink = (id: string) => {
    setSocialLinks(socialLinks.filter((link) => link.id !== id))
  }

  const handlePresetSelect = (presetId: string) => {
    setSelectedPreset(presetId)
    setShowPresetDialog(true)
  }

  const applyPreset = () => {
    if (!selectedPreset || !socialPresets[selectedPreset as keyof typeof socialPresets]) {
      return
    }

    const presetLinks = socialPresets[selectedPreset as keyof typeof socialPresets]

    if (presetApplyMode === "replace") {
      setSocialLinks([...presetLinks])
    } else {
      // Merge mode - add only links that don't exist by icon
      const currentIcons = socialLinks.map((link) => link.icon)
      const newLinks = presetLinks.filter((link) => !currentIcons.includes(link.icon))
      setSocialLinks([...socialLinks, ...newLinks])
    }

    setShowPresetDialog(false)
    toast({
      title: "Набор применен",
      description: `Набор "${selectedPreset}" успешно применен`,
    })
  }

  const saveChanges = () => {
    // В реальном приложении здесь был бы API запрос для сохранения данных
    // и загрузки файла логотипа на сервер
    toast({
      title: "Изменения сохранены",
      description: "Футер успешно обновлен",
    })
  }

  // Создаем превью для каждого набора
  const presetOptions = [
    {
      id: "basic",
      name: "Базовый",
      description: "Facebook, Twitter, Instagram",
      preview: (
        <div className="flex gap-2">
          <Icons.Facebook className="h-6 w-6" />
          <Icons.Twitter className="h-6 w-6" />
          <Icons.Instagram className="h-6 w-6" />
        </div>
      ),
    },
    {
      id: "business",
      name: "Бизнес",
      description: "Facebook, Twitter, LinkedIn, YouTube",
      preview: (
        <div className="flex gap-2">
          <Icons.Facebook className="h-6 w-6" />
          <Icons.Twitter className="h-6 w-6" />
          <Icons.Linkedin className="h-6 w-6" />
          <Icons.Youtube className="h-6 w-6" />
        </div>
      ),
    },
    {
      id: "creative",
      name: "Креативный",
      description: "Instagram, Dribbble, Pinterest, Behance",
      preview: (
        <div className="flex gap-2">
          <Icons.Instagram className="h-6 w-6" />
          <Icons.Dribbble className="h-6 w-6" />
          <Icons.Pin className="h-6 w-6" />
          <Icons.Figma className="h-6 w-6" />
        </div>
      ),
    },
    {
      id: "tech",
      name: "Технический",
      description: "GitHub, Twitter, LinkedIn, Discord",
      preview: (
        <div className="flex gap-2">
          <Icons.Github className="h-6 w-6" />
          <Icons.Twitter className="h-6 w-6" />
          <Icons.Linkedin className="h-6 w-6" />
          <Icons.MessageSquare className="h-6 w-6" />
        </div>
      ),
    },
    {
      id: "full",
      name: "Полный набор",
      description: "8 популярных социальных сетей",
      preview: (
        <div className="flex flex-wrap gap-1 justify-center">
          <Icons.Facebook className="h-5 w-5" />
          <Icons.Twitter className="h-5 w-5" />
          <Icons.Instagram className="h-5 w-5" />
          <Icons.Linkedin className="h-5 w-5" />
          <Icons.Youtube className="h-5 w-5" />
          <Icons.Pin className="h-5 w-5" />
          <Icons.Github className="h-5 w-5" />
          <Icons.Music2 className="h-5 w-5" />
        </div>
      ),
    },
  ]

  return (
    <div>
      <h1 className="text-3xl font-bold mb-8">Управление футером</h1>

      <Tabs defaultValue="sections" className="mb-8">
        <TabsList>
          <TabsTrigger value="sections">Разделы и ссылки</TabsTrigger>
          <TabsTrigger value="social">Социальные сети</TabsTrigger>
          <TabsTrigger value="logo">Логотип</TabsTrigger>
          <TabsTrigger value="colors">Цвета</TabsTrigger>
          <TabsTrigger value="copyright">Копирайт</TabsTrigger>
        </TabsList>

        <TabsContent value="sections">
          <div className="grid gap-6 md:grid-cols-2">
            {footerSections.map((section) => (
              <Card key={section.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <Input
                      value={section.title}
                      onChange={(e) => updateSectionTitle(section.id, e.target.value)}
                      className="font-bold text-lg"
                    />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {section.links.map((link) => (
                      <div key={link.id} className="grid grid-cols-[1fr_1fr_auto] gap-2 items-center">
                        <div>
                          <Label htmlFor={`${section.id}-${link.id}-text`} className="sr-only">
                            Текст ссылки
                          </Label>
                          <Input
                            id={`${section.id}-${link.id}-text`}
                            value={link.text}
                            onChange={(e) => updateLinkText(section.id, link.id, e.target.value)}
                            placeholder="Текст ссылки"
                          />
                        </div>
                        <div>
                          <Label htmlFor={`${section.id}-${link.id}-url`} className="sr-only">
                            URL ссылки
                          </Label>
                          <Input
                            id={`${section.id}-${link.id}-url`}
                            value={link.url}
                            onChange={(e) => updateLinkUrl(section.id, link.id, e.target.value)}
                            placeholder="URL"
                          />
                        </div>
                        <Button
                          variant="destructive"
                          size="icon"
                          onClick={() => removeLink(section.id, link.id)}
                          className="h-10 w-10"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                    <Button variant="outline" size="sm" onClick={() => addLink(section.id)} className="w-full">
                      <Plus className="mr-2 h-4 w-4" />
                      Добавить ссылку
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="social">
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Готовые наборы социальных сетей</CardTitle>
            </CardHeader>
            <CardContent>
              <PresetSelector options={presetOptions} value={selectedPreset} onChange={handlePresetSelect} />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Ссылки на социальные сети</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <Label htmlFor="social-icon-size">Размер иконок</Label>
                    <div className="flex items-center gap-2">
                      <Input
                        id="social-icon-size"
                        type="range"
                        min="3"
                        max="8"
                        value={socialIconSize}
                        onChange={(e) => setSocialIconSize(Number.parseInt(e.target.value))}
                        className="w-full"
                      />
                      <span className="w-8 text-center">{socialIconSize}</span>
                    </div>
                  </div>
                  <div>
                    <Label>Цвет иконок</Label>
                    <ColorPicker value={socialIconColor} onChange={setSocialIconColor} />
                  </div>
                  <div>
                    <Label>Цвет при наведении</Label>
                    <ColorPicker value={socialIconHoverColor} onChange={setSocialIconHoverColor} />
                  </div>
                </div>

                <div className="space-y-4 mt-6">
                  <Label>Ссылки на социальные сети</Label>
                  {socialLinks.map((link) => {
                    const IconComponent = (Icons as any)[link.icon] || Icons.Globe
                    return (
                      <div key={link.id} className="grid grid-cols-[auto_1fr_2fr_auto] gap-2 items-center">
                        <div className="flex items-center justify-center">
                          <GripVertical className="h-5 w-5 text-muted-foreground cursor-move" />
                        </div>
                        <div>
                          <IconSelect
                            value={link.icon}
                            onChange={(value) => updateSocialLink(link.id, "icon", value)}
                          />
                        </div>
                        <div className="grid grid-cols-2 gap-2">
                          <Input
                            value={link.label}
                            onChange={(e) => updateSocialLink(link.id, "label", e.target.value)}
                            placeholder="Название"
                          />
                          <Input
                            value={link.url}
                            onChange={(e) => updateSocialLink(link.id, "url", e.target.value)}
                            placeholder="URL"
                          />
                        </div>
                        <Button
                          variant="destructive"
                          size="icon"
                          onClick={() => removeSocialLink(link.id)}
                          className="h-10 w-10"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    )
                  })}
                  <Button variant="outline" onClick={addSocialLink} className="w-full">
                    <Plus className="mr-2 h-4 w-4" />
                    Добавить социальную сеть
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="logo">
          <Card>
            <CardHeader>
              <CardTitle>Логотип в футере</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="flex items-center space-x-2">
                  <Switch id="show-logo" checked={showLogoInFooter} onCheckedChange={setShowLogoInFooter} />
                  <Label htmlFor="show-logo">Показывать логотип в футере</Label>
                </div>

                {showLogoInFooter && (
                  <div>
                    <Label htmlFor="logo-upload" className="mb-2 block">
                      Загрузить логотип
                    </Label>
                    <FileUpload id="logo-upload" onFileChange={handleLogoChange} previewUrl={logoUrl || undefined} />
                    <p className="mt-2 text-sm text-muted-foreground">
                      Рекомендуемый размер: 180x60 пикселей. Поддерживаемые форматы: PNG, JPG, SVG.
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="colors">
          <Card>
            <CardHeader>
              <CardTitle>Настройка цветов футера</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 md:grid-cols-2">
                <ColorPicker
                  label="Цвет фона"
                  value={footerColors.background}
                  onChange={(value) => updateFooterColor("background", value)}
                />
                <ColorPicker
                  label="Цвет текста"
                  value={footerColors.text}
                  onChange={(value) => updateFooterColor("text", value)}
                />
                <ColorPicker
                  label="Цвет заголовков"
                  value={footerColors.headings}
                  onChange={(value) => updateFooterColor("headings", value)}
                />
                <ColorPicker
                  label="Цвет ссылок"
                  value={footerColors.links}
                  onChange={(value) => updateFooterColor("links", value)}
                />
                <ColorPicker
                  label="Цвет ссылок при наведении"
                  value={footerColors.linksHover}
                  onChange={(value) => updateFooterColor("linksHover", value)}
                />
                <ColorPicker
                  label="Цвет границы"
                  value={footerColors.border}
                  onChange={(value) => updateFooterColor("border", value)}
                />
              </div>
              <Button variant="outline" onClick={resetColors} className="mt-6">
                Сбросить цвета
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="copyright">
          <Card>
            <CardHeader>
              <CardTitle>Текст копирайта</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="copyright">Текст копирайта</Label>
                  <Input
                    id="copyright"
                    value={copyrightText}
                    onChange={(e) => setCopyrightText(e.target.value)}
                    placeholder="© 2023 Название компании. Все права защищены."
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="flex justify-end">
        <Button onClick={saveChanges}>Сохранить изменения</Button>
      </div>

      <Card className="mt-8">
        <CardHeader>
          <CardTitle>Предпросмотр футера</CardTitle>
        </CardHeader>
        <CardContent>
          <div
            className="border rounded-lg p-6"
            style={{
              backgroundColor: footerColors.background,
              color: footerColors.text,
              borderColor: footerColors.border,
            }}
          >
            {showLogoInFooter && logoUrl && (
              <div className="mb-8">
                <img src={logoUrl || "/placeholder.svg"} alt="Логотип компании" className="max-h-16" />
              </div>
            )}
            <div className="grid gap-8 sm:grid-cols-2 md:grid-cols-4">
              {footerSections.map((section) => (
                <div key={section.id} className="space-y-3">
                  <h3 style={{ color: footerColors.headings }} className="font-medium">
                    {section.title}
                  </h3>
                  <ul className="space-y-2">
                    {section.links.map((link) => (
                      <li key={link.id}>
                        <a
                          href={link.url}
                          style={{ color: footerColors.links }}
                          className="text-sm hover:text-gray-900 transition-colors"
                          onMouseOver={(e) => (e.currentTarget.style.color = footerColors.linksHover)}
                          onMouseOut={(e) => (e.currentTarget.style.color = footerColors.links)}
                        >
                          {link.text}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>

            {socialLinks.length > 0 && (
              <div className="mt-8 flex justify-center gap-4">
                {socialLinks.map((link) => {
                  const IconComponent = (Icons as any)[link.icon] || Icons.Globe
                  return (
                    <a
                      key={link.id}
                      href={link.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      title={link.label}
                      style={{ color: socialIconColor }}
                      className="transition-colors"
                      onMouseOver={(e) => (e.currentTarget.style.color = socialIconHoverColor)}
                      onMouseOut={(e) => (e.currentTarget.style.color = socialIconColor)}
                    >
                      <IconComponent style={{ height: `${socialIconSize * 4}px`, width: `${socialIconSize * 4}px` }} />
                      <span className="sr-only">{link.label}</span>
                    </a>
                  )
                })}
              </div>
            )}

            <div className="mt-8 pt-6" style={{ borderTopColor: footerColors.border, borderTopWidth: "1px" }}>
              <p className="text-sm" style={{ color: footerColors.text }}>
                {copyrightText}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Диалог для выбора режима применения пресета */}
      <Dialog open={showPresetDialog} onOpenChange={setShowPresetDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Применить набор социальных сетей</DialogTitle>
            <DialogDescription>Выберите, как вы хотите применить выбранный набор социальных сетей.</DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <RadioGroup
              value={presetApplyMode}
              onValueChange={(value) => setPresetApplyMode(value as "replace" | "merge")}
            >
              <div className="flex items-start space-x-2 mb-4">
                <RadioGroupItem value="replace" id="replace" />
                <div>
                  <Label htmlFor="replace" className="font-medium">
                    Заменить существующие
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Удалить все текущие ссылки и заменить их выбранным набором.
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-2">
                <RadioGroupItem value="merge" id="merge" />
                <div>
                  <Label htmlFor="merge" className="font-medium">
                    Добавить к существующим
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Добавить новые ссылки из набора, сохранив существующие.
                  </p>
                </div>
              </div>
            </RadioGroup>

            {presetApplyMode === "replace" && socialLinks.length > 0 && (
              <div className="flex items-center mt-4 p-3 bg-amber-50 text-amber-800 rounded-md">
                <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0" />
                <p className="text-sm">
                  Внимание! Это действие заменит все {socialLinks.length} существующих ссылок на социальные сети.
                </p>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowPresetDialog(false)}>
              Отмена
            </Button>
            <Button onClick={applyPreset}>Применить</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
