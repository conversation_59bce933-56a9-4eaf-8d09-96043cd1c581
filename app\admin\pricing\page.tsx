"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { Switch } from "@/components/ui/switch"

interface PricingPlan {
  id: string
  name: string
  price: number
  description: string
  features: string[]
  isPopular: boolean
}

export default function AdminPricingPage() {
  const { toast } = useToast()
  const [plans, setPlans] = useState<PricingPlan[]>([
    {
      id: "starter",
      name: "Starter",
      price: 29,
      description: "Perfect for small teams just getting started",
      features: ["Up to 5 users", "Basic analytics", "24-hour support", "1GB storage"],
      isPopular: false,
    },
    {
      id: "professional",
      name: "Professional",
      price: 79,
      description: "Ideal for growing businesses",
      features: ["Up to 20 users", "Advanced analytics", "Priority support", "10GB storage", "Custom integrations"],
      isPopular: true,
    },
    {
      id: "enterprise",
      name: "Enterprise",
      price: 199,
      description: "For large organizations with specific needs",
      features: [
        "Unlimited users",
        "Enterprise analytics",
        "24/7 dedicated support",
        "Unlimited storage",
        "Custom integrations",
        "On-premise option",
        "SLA guarantee",
      ],
      isPopular: false,
    },
  ])

  const updatePlan = (id: string, field: keyof PricingPlan, value: any) => {
    setPlans(plans.map((plan) => (plan.id === id ? { ...plan, [field]: value } : plan)))
  }

  const updateFeature = (planId: string, index: number, value: string) => {
    setPlans(
      plans.map((plan) => {
        if (plan.id === planId) {
          const newFeatures = [...plan.features]
          newFeatures[index] = value
          return { ...plan, features: newFeatures }
        }
        return plan
      }),
    )
  }

  const addFeature = (planId: string) => {
    setPlans(
      plans.map((plan) => {
        if (plan.id === planId) {
          return { ...plan, features: [...plan.features, ""] }
        }
        return plan
      }),
    )
  }

  const removeFeature = (planId: string, index: number) => {
    setPlans(
      plans.map((plan) => {
        if (plan.id === planId) {
          return { ...plan, features: plan.features.filter((_, i) => i !== index) }
        }
        return plan
      }),
    )
  }

  const savePlans = () => {
    toast({
      title: "Цены обновлены",
      description: "Все изменения успешно сохранены",
    })
  }

  return (
    <div>
      <h1 className="text-3xl font-bold mb-8">Управление ценами</h1>

      <div className="grid gap-6 md:grid-cols-3 mb-8">
        {plans.map((plan) => (
          <Card key={plan.id} className={plan.isPopular ? "border-primary" : ""}>
            <CardHeader>
              <CardTitle>
                <Input
                  value={plan.name}
                  onChange={(e) => updatePlan(plan.id, "name", e.target.value)}
                  className="font-bold text-lg"
                />
              </CardTitle>
              <div className="flex items-center gap-2 mt-2">
                <Switch
                  checked={plan.isPopular}
                  onCheckedChange={(checked) => updatePlan(plan.id, "isPopular", checked)}
                />
                <Label>Популярный тариф</Label>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label>Цена ($/месяц)</Label>
                <Input
                  type="number"
                  value={plan.price}
                  onChange={(e) => updatePlan(plan.id, "price", Number.parseInt(e.target.value))}
                />
              </div>

              <div>
                <Label>Описание</Label>
                <Input value={plan.description} onChange={(e) => updatePlan(plan.id, "description", e.target.value)} />
              </div>

              <div>
                <Label>Функции</Label>
                <div className="space-y-2 mt-2">
                  {plan.features.map((feature, index) => (
                    <div key={index} className="flex gap-2">
                      <Input
                        value={feature}
                        onChange={(e) => updateFeature(plan.id, index, e.target.value)}
                        placeholder="Функция"
                      />
                      <Button variant="destructive" size="sm" onClick={() => removeFeature(plan.id, index)}>
                        ×
                      </Button>
                    </div>
                  ))}
                  <Button variant="outline" size="sm" onClick={() => addFeature(plan.id)} className="w-full">
                    Добавить функцию
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Button onClick={savePlans} className="w-full md:w-auto">
        Сохранить все изменения
      </Button>
    </div>
  )
}
