"use client"

import type React from "react"
import Link from "next/link"
// Обновляем импорт иконок, добавляя иконку Plug для интеграций
import {
  LayoutDashboard,
  Settings,
  Eye,
  LogOut,
  ChevronDown,
  ChevronRight,
  Globe,
  Sliders,
  Shield,
  Building2,
  Plug,
} from "lucide-react"
import { useState } from "react"

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const [isSettingsOpen, setIsSettingsOpen] = useState(false)

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <div className="w-64 bg-white shadow-md">
        <div className="p-4">
          <h2 className="text-2xl font-bold text-gray-800">Админ панель</h2>
        </div>
        {/* В навигационном меню добавляем ссылку на раздел "Организация" после "Дашборд" */}
        <nav className="mt-8">
          <Link href="/admin" className="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-200">
            <LayoutDashboard className="mr-3 h-5 w-5" />
            Дашборд
          </Link>

          <Link href="/admin/organization" className="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-200">
            <Building2 className="mr-3 h-5 w-5" />
            Организация
          </Link>

          {/* Настройки dropdown section */}
          <div>
            <button
              onClick={() => setIsSettingsOpen(!isSettingsOpen)}
              className="flex w-full items-center justify-between px-4 py-2 text-gray-700 hover:bg-gray-200"
            >
              <div className="flex items-center">
                <Sliders className="mr-3 h-5 w-5" />
                Настройки
              </div>
              {isSettingsOpen ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
            </button>

            {isSettingsOpen && (
              <div className="pl-4">
                <Link
                  href="/admin/settings/landing"
                  className="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-200"
                >
                  <Globe className="mr-3 h-5 w-5" />
                  Настройки лендинга
                </Link>
                <Link
                  href="/admin/settings/general"
                  className="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-200"
                >
                  <Settings className="mr-3 h-5 w-5" />
                  Общие настройки
                </Link>
                <Link
                  href="/admin/settings/permissions"
                  className="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-200"
                >
                  <Shield className="mr-3 h-5 w-5" />
                  Разрешения
                </Link>
                <Link
                  href="/admin/settings/integration"
                  className="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-200"
                >
                  <Plug className="mr-3 h-5 w-5" />
                  Интеграции
                </Link>
              </div>
            )}
          </div>
        </nav>
        <div className="absolute bottom-0 w-64 p-4 border-t">
          <Link href="/" className="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-200 rounded">
            <Eye className="mr-3 h-5 w-5" />
            Просмотр сайта
          </Link>
          <button className="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-200 rounded w-full">
            <LogOut className="mr-3 h-5 w-5" />
            Выйти
          </button>
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-8">{children}</div>
      </div>
    </div>
  )
}
