"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { Trash2, Plus } from "lucide-react"

interface Feature {
  id: number
  title: string
  description: string
  icon: string
}

export default function AdminFeaturesPage() {
  const { toast } = useToast()
  const [features, setFeatures] = useState<Feature[]>([
    {
      id: 1,
      title: "Advanced Analytics",
      description: "Gain valuable insights with our powerful analytics tools.",
      icon: "BarChart3",
    },
    {
      id: 2,
      title: "Time Saving",
      description: "Automate repetitive tasks and streamline your workflow.",
      icon: "Clock",
    },
    {
      id: 3,
      title: "Seamless Integration",
      description: "Connect with your favorite tools and services.",
      icon: "RefreshCw",
    },
  ])

  const [newFeature, setNewFeature] = useState({
    title: "",
    description: "",
    icon: "",
  })

  const addFeature = () => {
    if (newFeature.title && newFeature.description) {
      setFeatures([
        ...features,
        {
          id: Date.now(),
          ...newFeature,
        },
      ])
      setNewFeature({ title: "", description: "", icon: "" })
      toast({
        title: "Функция добавлена",
        description: "Новая функция успешно добавлена",
      })
    }
  }

  const deleteFeature = (id: number) => {
    setFeatures(features.filter((f) => f.id !== id))
    toast({
      title: "Функция удалена",
      description: "Функция успешно удалена",
    })
  }

  return (
    <div>
      <h1 className="text-3xl font-bold mb-8">Управление функциями</h1>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Добавить новую функцию</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <Label htmlFor="title">Название</Label>
              <Input
                id="title"
                value={newFeature.title}
                onChange={(e) => setNewFeature({ ...newFeature, title: e.target.value })}
                placeholder="Название функции"
              />
            </div>
            <div>
              <Label htmlFor="icon">Иконка (название из Lucide)</Label>
              <Input
                id="icon"
                value={newFeature.icon}
                onChange={(e) => setNewFeature({ ...newFeature, icon: e.target.value })}
                placeholder="Например: BarChart3"
              />
            </div>
            <div className="md:col-span-2">
              <Label htmlFor="description">Описание</Label>
              <Textarea
                id="description"
                value={newFeature.description}
                onChange={(e) => setNewFeature({ ...newFeature, description: e.target.value })}
                placeholder="Описание функции"
                rows={3}
              />
            </div>
            <div className="md:col-span-2">
              <Button onClick={addFeature} className="w-full">
                <Plus className="mr-2 h-4 w-4" />
                Добавить функцию
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid gap-4">
        <h2 className="text-xl font-semibold">Текущие функции</h2>
        {features.map((feature) => (
          <Card key={feature.id}>
            <CardContent className="flex items-center justify-between p-4">
              <div className="flex-1">
                <h3 className="font-semibold">{feature.title}</h3>
                <p className="text-sm text-muted-foreground">{feature.description}</p>
                <p className="text-xs text-muted-foreground mt-1">Иконка: {feature.icon}</p>
              </div>
              <Button variant="destructive" size="sm" onClick={() => deleteFeature(feature.id)}>
                <Trash2 className="h-4 w-4" />
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
