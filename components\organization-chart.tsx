"use client"

import { useEffect } from "react"
import React<PERSON>low, { useNodesState, useEdgesState, Controls, Background, BackgroundVariant, type Node, type Edge } from "reactflow"
import "reactflow/dist/style.css"

// Типы данных
interface Department {
  id: string
  name: string
}

interface Employee {
  id: string
  name: string
  position: string
  department: string
  email: string
  phone: string
  status: "active" | "vacation" | "sick" | "remote"
  joinDate: string
  managerId?: string
}

interface OrgInfo {
  name: string
}

interface OrganizationChartProps {
  departments: Department[]
  employees: Employee[]
  orgInfo: OrgInfo
  layout?: "hierarchical" | "orgchart" | "network"
}

// Кастомные ноды (пример)
const CustomNode = ({ data }: { data: any }) => {
  return (
    <div style={{ border: "1px solid #222", padding: "10px" }}>
      <div>{data.label}</div>
    </div>
  )
}

const nodeTypes = {
  custom: CustomNode,
}

export function OrganizationChart({
  departments,
  employees,
  orgInfo,
  layout = "hierarchical",
}: OrganizationChartProps) {
  // Состояния для узлов и связей
  const [nodes, setNodes, onNodesChange] = useNodesState([])
  const [edges, setEdges, onEdgesChange] = useEdgesState([])

  useEffect(() => {
    // Преобразование данных в формат React Flow
    const newNodes: Node[] = departments.map((department) => ({
      id: department.id,
      data: { label: department.name },
      position: { x: 0, y: 0 },
      type: "custom",
    }))

    const newEdges: Edge[] = employees
      .filter((employee) => employee.managerId)
      .map((employee) => ({
        id: `e-${employee.id}-${employee.managerId}`,
        source: employee.id,
        target: employee.managerId!,
      }))

    setNodes(newNodes)
    setEdges(newEdges)
  }, [departments, employees, setNodes, setEdges])

  return (
    <div style={{ width: "100%", height: "500px" }}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        nodeTypes={nodeTypes}
        fitView
      >
        <Controls />
        <Background variant={BackgroundVariant.Dots} gap={12} size={1} />
      </ReactFlow>
    </div>
  )
}
